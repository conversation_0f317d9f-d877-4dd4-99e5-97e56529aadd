import DiscountCodeComponent from '@/components/discountCode';
import { Minus, Plus } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Bars } from 'react-loader-spinner';
import { useRouter } from 'next/navigation';
import { GameNames } from "@/constants/common";
import {  MarvelRivalsServiceTypes } from "@/constants/service-types";
import { createOrder } from "@/database/functions.database";
import { calcTotalPricePrecentage } from "@/utils/now-payments";
import { errorToast, successToast } from "@/utils/toast-utils";
import CustomDropDown from '@/components/custom-dropdown';

// Custom Dropdown Component
interface DropdownOption {
  id: string;
  label: string;
  title: string;
  value?: number;
  action?: string;
}

interface CustomDropDownProps {
  options: DropdownOption[];
  placeholder: string;
  selectedValue: DropdownOption | null;
  onSelect: (option: DropdownOption) => void;
  className?: string;
}

// Multi-Range Slider Component
interface MultiRangeSliderProps {
  min: number;
  max: number;
  currentLevel: number;
  desiredLevel: number;
  onCurrentChange: (value: number) => void;
  onDesiredChange: (value: number) => void;
}

const MultiRangeSlider: React.FC<MultiRangeSliderProps> = ({ min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange }) => {
  const sliderRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<'current' | 'desired' | null>(null);

  const getPercent = useCallback((value: number) => Math.round(((value - min) / (max - min)) * 100), [min, max]);

  const handleMouseDown = (type: 'current' | 'desired') => (event: React.MouseEvent) => {
    setIsDragging(type);
    event.preventDefault();
  };

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isDragging || !sliderRef.current) return;

    const rect = sliderRef.current.getBoundingClientRect();
    const percent = Math.min(100, Math.max(0, ((event.clientX - rect.left) / rect.width) * 100));
    const value = Math.round(min + (percent / 100) * (max - min));

    if (isDragging === 'current') {
      const newCurrent = Math.min(value, desiredLevel - 1);
      onCurrentChange(Math.max(min, newCurrent));
    } else if (isDragging === 'desired') {
      const newDesired = Math.max(value, currentLevel + 1);
      onDesiredChange(Math.min(max, newDesired));
    }
  }, [isDragging, min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(null);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const currentPercent = getPercent(currentLevel);
  const desiredPercent = getPercent(desiredLevel);

  return (
    <div className="px-8 mb-6">
      <div className="relative mb-4" ref={sliderRef}>
        {/* Track container */}
        <div className="relative h-6 bg-secondary-dark rounded-lg border border-secondary-dark">
          {/* Progress between current and desired */}
          <div
            className="absolute h-6 bg-gradient-to-r from-primary-yellow to-primary-orange rounded-lg transition-all duration-300"
            style={{
              left: `${currentPercent}%`,
              width: `${desiredPercent - currentPercent}%`
            }}
          />

          {/* Current level handle */}
          <div
            className="absolute w-6 h-6 bg-primary-yellow rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-primary-yellow/80 z-20"
            style={{
              left: `${currentPercent}%`,
              transform: 'translateX(-50%)'
            }}
            onMouseDown={handleMouseDown('current')}
          >
            <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-primary-yellow text-black px-2 py-1 rounded text-xs font-bold whitespace-nowrap font-montserrat">
              Current: {currentLevel}
            </div>
          </div>

          {/* Desired level handle */}
          <div
            className="absolute w-6 h-6 bg-primary-orange rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-primary-orange/80 z-20"
            style={{
              left: `${desiredPercent}%`,
              transform: 'translateX(-50%)'
            }}
            onMouseDown={handleMouseDown('desired')}
          >
            <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-primary-orange text-white px-2 py-1 rounded text-xs font-bold whitespace-nowrap font-montserrat">
              Target: {desiredLevel}
            </div>
          </div>
        </div>
      </div>

      {/* Level markers */}
      <div className="flex justify-between text-sm text-gray-400 mb-4 font-montserrat">
        <div className="text-center">
          <div className="font-bold">1</div>
          <div className="text-xs">START</div>
        </div>
        <div className="text-center">
          <div className="font-bold">25</div>
          <div className="text-xs">LOW</div>
        </div>
        <div className="text-center">
          <div className="font-bold">50</div>
          <div className="text-xs">MID</div>
        </div>
        <div className="text-center">
          <div className="font-bold">75</div>
          <div className="text-xs">HIGH</div>
        </div>
        <div className="text-center">
          <div className="font-bold">100</div>
          <div className="text-xs">MAX</div>
        </div>
      </div>
    </div>
  );
};

interface LevelBoostingComponentProps {
  priceControl?: any;
}

const LevelBoostingComponent: React.FC<LevelBoostingComponentProps> = ({ priceControl }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [discount, setDiscount] = useState(1);

  // Level states
  const [currentLevel, setCurrentLevel] = useState(21);
  const [desiredLevel, setDesiredLevel] = useState(64);
  
  // Mock data
  const LolLevelBoostingDropDown: DropdownOption[] = [
    { id: "acc1", label: "ACC SHARED", title: "ACC SHARED", value: 0, action: "None" },
    { id: "acc2", label: "PLAY W BOOSTERS (100%)", title: "PLAY W BOOSTERS (100%)", value: 100, action: "Increase" },
    { id: "acc3", label: "PLAY W BOOSTERS (200%)", title: "PLAY W BOOSTERS (200%)", value: 200, action: "Increase" },
  ];

  const platformOptions: DropdownOption[] = [
    { id: "pc", label: "PC", title: "PC" },
    { id: "console", label: "CONSOLE", title: "CONSOLE" },
  ];

  const roleOptions: DropdownOption[] = [
    { id: "any", label: "ANY ROLE", title: "ANY ROLE" },
    { id: "tank", label: "TANK", title: "TANK" },
    { id: "damage", label: "DAMAGE", title: "DAMAGE" },
    { id: "support", label: "SUPPORT", title: "SUPPORT" },
  ];

  // Selection states
  const [accShared, setAccShared] = useState<DropdownOption | null>(null);
  const [platform, setPlatform] = useState<DropdownOption | null>(null);
  const [role, setRole] = useState<DropdownOption | null>(null);
  
  // Price states
  const [basePrice, setBasePrice] = useState(0);
  const [accSharedPrice, setAccSharedPrice] = useState(0);
  const [boostersPrice, setBoostersPrice] = useState(0);
  const [priorityPrice, setPriorityPrice] = useState(0);
  const [streamPrice, setStreamPrice] = useState(0);
  const [completionTime, setCompletionTime] = useState({ days: 0, hours: 0 });
  const [total, setTotal] = useState<any>({ price: "5.21" });
  const [priceCt, setPriceCt] = useState<any>({});
  
  // Add-on states
  const [boosters, setBoosters] = useState(false);
  const [offline, setOffline] = useState(false);
  const [roles, setRoles] = useState(false);
  const [priority, setPriority] = useState(false);
  const [stream, setStream] = useState(false);

  // Level adjustment functions
  const adjustCurrentLevel = (increment: number) => {
    setCurrentLevel(prev => {
      const newLevel = Math.max(1, Math.min(99, prev + increment));
      if (newLevel >= desiredLevel) {
        setDesiredLevel(Math.min(100, newLevel + 1));
      }
      return newLevel;
    });
  };

  const adjustDesiredLevel = (increment: number) => {
    setDesiredLevel(prev => {
      const newLevel = Math.max(currentLevel + 1, Math.min(100, prev + increment));
      return newLevel;
    });
  };

  // Calculate price based on level difference
  const calculateBasePrice = useCallback(() => {
    const levelDifference = desiredLevel - currentLevel;
    if (levelDifference <= 0) {
      return 5.21;
    }
    
    const maxPrice = 2681.80;
    const minPrice = 5.21;
    const priceRange = maxPrice - minPrice;
    
    const calculatedPrice = minPrice + ((levelDifference - 1) / 98) * priceRange;
    
    return Math.max(minPrice, calculatedPrice);
  }, [currentLevel, desiredLevel]);

  // Calculate completion time
  const calculateCompletionTime = useCallback(() => {
    const levelDifference = desiredLevel - currentLevel;
    if (levelDifference <= 0) {
      return { days: 0, hours: 1 };
    }
    
    const maxDays = 73;
    const maxHours = 5;
    const totalMaxHours = (maxDays * 24) + maxHours;
    
    const totalHours = (levelDifference / 99) * totalMaxHours;
    
    const days = Math.floor(totalHours / 24);
    const hours = Math.round(totalHours % 24);
    
    return { days, hours };
  }, [currentLevel, desiredLevel]);

  // Handle ACC SHARED selection
  const handleAccSharedChange = (option: DropdownOption) => {
    setAccShared(option);
    
    if (option.label === "PLAY W BOOSTERS (100%)") {
      setAccSharedPrice(basePrice * 1.0);
    } else if (option.label === "PLAY W BOOSTERS (200%)") {
      setAccSharedPrice(basePrice * 2.0);
    } else {
      setAccSharedPrice(0);
    }
  };

  // Handle addon changes with proper price calculation
  const handleAddonChange = (
    isEnabled: boolean,
    percentage: number,
    setPriceFunction: (price: number) => void
  ) => {
    if (isEnabled) {
      setPriceFunction(basePrice * percentage);
    } else {
      setPriceFunction(0);
    }
  };

  // Calculate total price
  const updateTotalPrice = useCallback(() => {
    const subtotal = basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice;
    const finalPrice = subtotal * discount;
    
    if (priceCt && Object.keys(priceCt).length > 0) {
      try {
        const result = calcTotalPricePrecentage(priceCt, finalPrice);
        setTotal(result);
      } catch (error) {
        console.error('Price calculation error:', error);
        setTotal({ price: finalPrice.toFixed(2) });
      }
    } else {
      setTotal({ price: finalPrice.toFixed(2) });
    }
  }, [basePrice, accSharedPrice, boostersPrice, priorityPrice, streamPrice, discount, priceCt]);

  // Handle order creation with proper validation and error handling
  const handleSubmit = async () => {
    // Validation
    if (desiredLevel <= currentLevel) {
      errorToast("Desired level must be higher than current level!");
      return;
    }
    
    if (!accShared) {
      errorToast("Please select account sharing option!");
      return;
    }
    
    if (!platform) {
      errorToast("Please select a platform!");
      return;
    }
    
    if (!role) {
      errorToast("Please select a role!");
      return;
    }
    
    setLoading(true);
    
    try {
      const orderId = await createOrder({
        game_name: GameNames.LeagueOfLegends, // Adjust as needed
        service_type: MarvelRivalsServiceTypes.LevelBoosting, // Adjust as needed
        order_details: JSON.stringify({
          currentLevel,
          desiredLevel,
          levelDifference: desiredLevel - currentLevel,
          platform: platform.label,
          role: role.label,
          accShared: accShared.label,
          addons: {
            boosters,
            offline,
            roles,
            priority,
            stream
          }
        }),
        play_with_boosters: boosters,
        appear_offline: offline,
        champions_roles: roles,
        priority_order: priority,
        stream_game: stream,
        completion_time_hours: completionTime.days * 24 + completionTime.hours,
        price: parseFloat(total?.price) || basePrice
      });

      if (orderId) {
        successToast("Order created successfully!");
        router.push(`/checkout?order_id=${orderId}`);
      } else {
        errorToast("Failed to create order. Please try again.");
      }
    } catch (error) {
      console.error('Order creation failed:', error);
      errorToast("Failed to create order. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Get price control
  const getPriceControl = useCallback(async () => {
    try {
      setPriceCt(priceControl || {});
    } catch (error) {
      console.error('Price control error:', error);
      setPriceCt({});
    }
  }, [priceControl]);

  // Effects
  useEffect(() => {
    const newPrice = calculateBasePrice();
    setBasePrice(newPrice);
    const newTime = calculateCompletionTime();
    setCompletionTime(newTime);
  }, [calculateBasePrice, calculateCompletionTime]);

  useEffect(() => {
    updateTotalPrice();
  }, [updateTotalPrice]);

  // Update prices when base price changes
  useEffect(() => {
    if (accShared) {
      handleAccSharedChange(accShared);
    }
  }, [basePrice]);

  // Handle addon price changes
  useEffect(() => {
    handleAddonChange(boosters, 0.4, setBoostersPrice);
  }, [boosters, basePrice]);

  useEffect(() => {
    handleAddonChange(priority, 0.2, setPriorityPrice);
  }, [priority, basePrice]);

  useEffect(() => {
    handleAddonChange(stream, 0.1, setStreamPrice);
  }, [stream, basePrice]);

  useEffect(() => {
    getPriceControl();
  }, [getPriceControl]);

  return (
    <div className="col-span-12 ">
      <div className="container mx-auto p-4">
        <div className="grid grid-cols-12 gap-3">
          {/* Main Content */}
          <div className="col-span-12 lg:col-span-9 bg-primary-dark rounded-md px-6 py-8 border border-secondary-dark">

            {/* Step 1: Level Selection */}
            <div className="mb-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center text-xl font-bold mr-4 text-white">
                  1
                </div>
                <h2 className="text-2xl font-bold text-white font-montserrat">SELECT YOUR CURRENT AND DESIRED LEVEL</h2>
              </div>
              
              <div className="flex justify-center">
                <div className="w-full max-w-4xl">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    {/* Current Level */}
                    <div className="text-center">
                      <h3 className="text-xl font-semibold mb-4 text-white font-montserrat">CURRENT LEVEL</h3>
                      <div className="text-6xl font-bold mb-4 text-primary-yellow font-montserrat">{currentLevel}</div>
                      <div className="inline-flex items-center gap-4 mb-4">
                        <button
                          onClick={() => adjustCurrentLevel(-1)}
                          className="w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white"
                          disabled={currentLevel <= 1}
                        >
                          <Minus size={20} />
                        </button>
                        <div className="bg-primary-yellow text-black px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat">
                          {currentLevel}
                        </div>
                        <button
                          onClick={() => adjustCurrentLevel(1)}
                          className="w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white"
                          disabled={currentLevel >= 99}
                        >
                          <Plus size={20} />
                        </button>
                      </div>
                    </div>

                    {/* Desired Level */}
                    <div className="text-center">
                      <h3 className="text-xl font-semibold mb-4 text-white font-montserrat">DESIRED LEVEL</h3>
                      <div className="text-6xl font-bold mb-4 text-primary-orange font-montserrat">{desiredLevel}</div>
                      <div className="inline-flex items-center gap-4 mb-4">
                        <button
                          onClick={() => adjustDesiredLevel(-1)}
                          className="w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white"
                          disabled={desiredLevel <= currentLevel + 1}
                        >
                          <Minus size={20} />
                        </button>
                        <div className="bg-primary-orange text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat">
                          {desiredLevel}
                        </div>
                        <button
                          onClick={() => adjustDesiredLevel(1)}
                          className="w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white"
                          disabled={desiredLevel >= 100}
                        >
                          <Plus size={20} />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Interactive Dual Level Slider */}
                  <MultiRangeSlider
                    min={1}
                    max={100}
                    currentLevel={currentLevel}
                    desiredLevel={desiredLevel}
                    onCurrentChange={setCurrentLevel}
                    onDesiredChange={setDesiredLevel}
                  />
                </div>
              </div>
            </div>

            {/* Steps 2, 3, 4 */}
            <div className="grid items-center grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white">
                    2
                  </div>
                  <div>
                    <h3 className="font-semibold text-white font-montserrat">ACC SHARED OR PLAY</h3>
                    <h3 className="font-semibold text-white font-montserrat">W BOOSTERS</h3>
                  </div>
                </div>
                <CustomDropDown
                  options={LolLevelBoostingDropDown}
                  selectedValue={accShared}
                  onSelect={handleAccSharedChange}
                  placeholder="ACC SHARED"
                />
              </div>

              <div>
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white">
                    3
                  </div>
                  <h3 className="font-semibold text-white font-montserrat">SELECT ROLE</h3>
                </div>
                <CustomDropDown
                  options={roleOptions}
                  selectedValue={role}
                  onSelect={setRole}
                  placeholder="ANY ROLE"
                />
              </div>

              <div>
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white">
                    4
                  </div>
                  <h3 className="font-semibold text-white font-montserrat">SELECT PLATFORM</h3>
                </div>
                <CustomDropDown
                  options={platformOptions}
                  selectedValue={platform}
                  onSelect={setPlatform}
                  placeholder="PC"
                />
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="col-span-12 lg:col-span-3 bg-primary-dark rounded-md p-2 border border-secondary-dark">
            {/* Level Summary */}
            <div className="bg-secondary-dark p-4 rounded-lg mb-6 flex items-center justify-center border border-secondary-dark">
              <div className="text-center">
                <div className="text-sm text-gray-400 mb-2 font-montserrat">BOOST FROM LEVEL</div>
                <div className="text-3xl font-bold mb-3 font-montserrat">
                  <span className="text-primary-yellow">{currentLevel}</span>
                  <span className="text-gray-400 mx-2">→</span>
                  <span className="text-primary-orange">{desiredLevel}</span>
                </div>
                <div className="bg-gradient-to-r from-primary-yellow to-primary-orange text-white px-4 py-2 rounded-full text-sm font-bold font-montserrat">
                  +{desiredLevel - currentLevel} LEVELS
                </div>
              </div>
            </div>

            {/* Add-ons */}
            <div className="space-y-4 mb-6">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400 font-montserrat">Play with Boosters (+40%)</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={boosters}
                    onChange={() => setBoosters(!boosters)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple"></div>
                </label>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400 font-montserrat">Appear Offline (Free)</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={offline}
                    onChange={() => setOffline(!offline)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple"></div>
                </label>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400 font-montserrat">Champions/Roles (Free)</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={roles}
                    onChange={() => setRoles(!roles)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple"></div>
                </label>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400 font-montserrat">Priority Order (+20%)</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={priority}
                    onChange={() => setPriority(!priority)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple"></div>
                </label>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400 font-montserrat">Stream Games (+10%)</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={stream}
                    onChange={() => setStream(!stream)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple"></div>
                </label>
              </div>
            </div>

            {/* Discount Code */}
            <DiscountCodeComponent setDiscount={setDiscount} discount={discount} />

            {/* Price Summary */}
            {discount < 1 ? (
              <div className="mb-4">
                {/* Original Price (crossed out) */}
                <div className="rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-2">
                  <span className="font-montserrat font-medium text-gray-400">Original Price</span>
                  <span className="font-montserrat font-medium text-gray-400 line-through">
                    ${(basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)}
                  </span>
                </div>
                
                {/* Discounted Price */}
                <div className="rounded-sm border border-green-500 p-3 flex justify-between items-center bg-[#1C1E2B]">
                  <span className="font-montserrat font-semibold text-green-400">Total Amount ({Math.round((1 - discount) * 100)}% OFF)</span>
                  <span className="font-montserrat font-semibold text-green-400">
                    ${total?.price || ((basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice) * discount).toFixed(2)}
                  </span>
                </div>
              </div>
            ) : (
              <div className="rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-4">
                <span className="font-montserrat font-semibold">Total Amount</span>
                <span className="font-montserrat font-semibold">
                  ${total?.price || (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)}
                </span>
              </div>
            )}

            {/* Order Button */}
            {loading ? (
              <button
                disabled
                type="button"
                className="flex justify-center items-center text-white bg-secondary-600 font-medium w-full rounded-lg text-sm me-2 mb-2 p-0"
              >
                <Bars
                  height="40"
                  color="white"
                  ariaLabel="bars-loading"
                  wrapperStyle={{}}
                  wrapperClass=""
                  visible={true}
                />
              </button>
            ) : (
              <button
                type="button"
                onClick={handleSubmit}
                className="text-white bg-primary-purple hover:bg-primary-shaded-purple focus:ring-4 w-full focus:ring-blue-300 font-medium rounded-lg text-sm px-7 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
              >
                Level Up Now
              </button>
            )}

            {/* Completion Time */}
            <div className="text-center text-xs text-gray-400 mt-4 font-montserrat">
              Completion Time: {completionTime.days} Days {completionTime.hours} Hours
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LevelBoostingComponent;