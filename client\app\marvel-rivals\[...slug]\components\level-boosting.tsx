import React, { useState, useCallback, useEffect } from 'react';
import { Minus, Plus } from 'lucide-react';

// Custom Dropdown Component
const CustomDropDown = ({ options, placeholder, selectedValue, onSelect, className = "" }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className={`relative ${className}`}>
      <div 
        className="bg-teal-600 text-white px-6 py-3 rounded-full cursor-pointer flex items-center justify-center min-w-[200px] hover:bg-teal-700 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="font-medium">
          {selectedValue?.label || placeholder}
        </span>
        <svg className="ml-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
        </svg>
      </div>
      
      {isOpen && (
        <div className="absolute top-full left-0 right-0 bg-slate-800 border border-slate-600 rounded-lg mt-1 z-50 shadow-lg">
          {options.map((option) => (
            <div
              key={option.id}
              className="px-4 py-2 hover:bg-slate-700 cursor-pointer text-white transition-colors"
              onClick={() => {
                onSelect(option);
                setIsOpen(false);
              }}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Discount Code Component
const DiscountCodeComponent = ({ setDiscount, discount }) => {
  const [code, setCode] = useState('');
  const [applied, setApplied] = useState(false);

  const applyDiscount = () => {
    if (code.toLowerCase() === 'discount10') {
      setDiscount(0.9);
      setApplied(true);
    } else if (code.toLowerCase() === 'save25') {
      setDiscount(0.75);
      setApplied(true);
    } else {
      alert('Invalid discount code. Try "discount10" or "save25"');
    }
  };

  const removeDiscount = () => {
    setDiscount(1);
    setApplied(false);
    setCode('');
  };

  return (
    <div className="my-4 p-3 bg-slate-800 rounded-lg">
      <div className="flex gap-2">
        <input
          type="text"
          value={code}
          onChange={(e) => setCode(e.target.value)}
          placeholder="Try: discount10 or save25"
          className="flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-violet-500"
          disabled={applied}
        />
        <button
          onClick={applied ? removeDiscount : applyDiscount}
          className={`px-4 py-2 rounded font-medium transition-colors ${
            applied 
              ? 'bg-red-600 hover:bg-red-700 text-white' 
              : 'bg-violet-600 hover:bg-violet-700 text-white'
          }`}
        >
          {applied ? 'Remove' : 'Apply'}
        </button>
      </div>
      {applied && (
        <p className="text-green-400 text-sm mt-1">
          Discount applied successfully! {Math.round((1 - discount) * 100)}% off
        </p>
      )}
    </div>
  );
};

const LevelBoostingComponent = () => {
  const [loading, setLoading] = useState(false);
  const [discount, setDiscount] = useState(1);
  
  // Level states
  const [currentLevel, setCurrentLevel] = useState(21);
  const [desiredLevel, setDesiredLevel] = useState(64);
  
  // Mock data
  const LolLevelBoostingDropDown = [
    { id: "acc1", label: "ACC SHARED", title: "ACC SHARED", value: 0, action: "None" },
    { id: "acc2", label: "PLAY W BOOSTERS (100%)", title: "PLAY W BOOSTERS (100%)", value: 100, action: "Increase" },
    { id: "acc3", label: "PLAY W BOOSTERS (200%)", title: "PLAY W BOOSTERS (200%)", value: 200, action: "Increase" },
  ];

  const platformOptions = [
    { id: "pc", label: "PC", title: "PC" },
    { id: "console", label: "CONSOLE", title: "CONSOLE" },
  ];

  const roleOptions = [
    { id: "any", label: "ANY ROLE", title: "ANY ROLE" },
    { id: "tank", label: "TANK", title: "TANK" },
    { id: "damage", label: "DAMAGE", title: "DAMAGE" },
    { id: "support", label: "SUPPORT", title: "SUPPORT" },
  ];
  
  // Selection states
  const [accShared, setAccShared] = useState(LolLevelBoostingDropDown[0]);
  const [platform, setPlatform] = useState(platformOptions[0]);
  const [role, setRole] = useState(roleOptions[0]);
  
  // Price states
  const [totalPrice, setTotalPrice] = useState(0);
  const [completionTime, setCompletionTime] = useState({ days: 0, hours: 0 });
  
  // Add-on states
  const [boosters, setBoosters] = useState(false);
  const [offline, setOffline] = useState(false);
  const [roles, setRoles] = useState(false);
  const [priority, setPriority] = useState(false);
  const [stream, setStream] = useState(false);

  // Level adjustment functions
  const adjustCurrentLevel = (increment) => {
    setCurrentLevel(prev => {
      const newLevel = Math.max(1, Math.min(99, prev + increment));
      if (newLevel >= desiredLevel) {
        setDesiredLevel(Math.min(100, newLevel + 1));
      }
      return newLevel;
    });
  };

  const adjustDesiredLevel = (increment) => {
    setDesiredLevel(prev => {
      const newLevel = Math.max(currentLevel + 1, Math.min(100, prev + increment));
      return newLevel;
    });
  };

  // Calculate price based on level difference
  const calculateBasePrice = useCallback(() => {
    const levelDifference = desiredLevel - currentLevel;
    if (levelDifference <= 0) {
      return 5.21;
    }
    
    const maxPrice = 2681.80;
    const minPrice = 5.21;
    const priceRange = maxPrice - minPrice;
    
    const calculatedPrice = minPrice + ((levelDifference - 1) / 98) * priceRange;
    
    return Math.max(minPrice, calculatedPrice);
  }, [currentLevel, desiredLevel]);

  // Calculate completion time
  const calculateCompletionTime = useCallback(() => {
    const levelDifference = desiredLevel - currentLevel;
    if (levelDifference <= 0) {
      return { days: 0, hours: 1 };
    }
    
    const maxDays = 73;
    const maxHours = 5;
    const totalMaxHours = (maxDays * 24) + maxHours;
    
    const totalHours = (levelDifference / 99) * totalMaxHours;
    
    const days = Math.floor(totalHours / 24);
    const hours = Math.round(totalHours % 24);
    
    return { days, hours };
  }, [currentLevel, desiredLevel]);

  // Calculate total price with add-ons
  const calculateTotalPrice = useCallback(() => {
    const basePrice = calculateBasePrice();
    let additionalPrice = 0;

    if (accShared.label === "PLAY W BOOSTERS (100%)") {
      additionalPrice += basePrice * 1.0;
    } else if (accShared.label === "PLAY W BOOSTERS (200%)") {
      additionalPrice += basePrice * 2.0;
    }

    if (boosters) additionalPrice += basePrice * 0.4;
    if (priority) additionalPrice += basePrice * 0.2;
    if (stream) additionalPrice += basePrice * 0.1;

    const subtotal = basePrice + additionalPrice;
    const finalPrice = subtotal * discount;
    
    return finalPrice;
  }, [calculateBasePrice, accShared.label, boosters, priority, stream, discount]);

  // Handle order creation
  const handleSubmit = async () => {
    if (desiredLevel <= currentLevel) {
      alert("Desired level must be higher than current level!");
      return;
    }
    
    setLoading(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert(`Order created successfully!\nLevel: ${currentLevel} → ${desiredLevel}\nTotal: $${totalPrice.toFixed(2)}`);
    } catch (error) {
      console.error('Order creation failed:', error);
      alert("Failed to create order. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    const time = calculateCompletionTime();
    setCompletionTime(time);
  }, [calculateCompletionTime]);

  useEffect(() => {
    const price = calculateTotalPrice();
    setTotalPrice(price);
  }, [calculateTotalPrice]);

  const originalPrice = calculateTotalPrice() / discount;

  return (
    <div className="col-span-12 bg-gray-100">
      <div className="container mx-auto p-4">
        <div className="grid grid-cols-12 gap-3">
          {/* Main Content */}
          <div className="col-span-12 lg:col-span-9 bg-slate-800 rounded-md px-6 py-8">
            
            {/* Step 1: Level Selection */}
            <div className="mb-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center text-xl font-bold mr-4">
                  1
                </div>
                <h2 className="text-2xl font-bold">SELECT YOUR CURRENT AND DESIRED LEVEL</h2>
              </div>
              
              <div className="flex justify-center">
                <div className="w-full max-w-4xl">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    {/* Current Level */}
                    <div className="text-center">
                      <h3 className="text-xl font-semibold mb-4">CURRENT LEVEL</h3>
                      <div className="text-6xl font-bold mb-4 text-gray-300">{currentLevel}</div>
                      <div className="inline-flex items-center gap-4 mb-4">
                        <button
                          onClick={() => adjustCurrentLevel(-1)}
                          className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center hover:bg-teal-700 transition-colors"
                          disabled={currentLevel <= 1}
                        >
                          <Minus size={20} />
                        </button>
                        <div className="bg-yellow-500 text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px]">
                          {currentLevel}
                        </div>
                        <button
                          onClick={() => adjustCurrentLevel(1)}
                          className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center hover:bg-teal-700 transition-colors"
                          disabled={currentLevel >= 99}
                        >
                          <Plus size={20} />
                        </button>
                      </div>
                    </div>

                    {/* Desired Level */}
                    <div className="text-center">
                      <h3 className="text-xl font-semibold mb-4">DESIRED LEVEL</h3>
                      <div className="text-6xl font-bold mb-4 text-orange-400">{desiredLevel}</div>
                      <div className="inline-flex items-center gap-4 mb-4">
                        <button
                          onClick={() => adjustDesiredLevel(-1)}
                          className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center hover:bg-teal-700 transition-colors"
                          disabled={desiredLevel <= currentLevel + 1}
                        >
                          <Minus size={20} />
                        </button>
                        <div className="bg-orange-500 text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px]">
                          {desiredLevel}
                        </div>
                        <button
                          onClick={() => adjustDesiredLevel(1)}
                          className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center hover:bg-teal-700 transition-colors"
                          disabled={desiredLevel >= 100}
                        >
                          <Plus size={20} />
                        </button>
                      </div>
                    </div>
                  </div>
                    
                  {/* Interactive Dual Level Slider */}
                  <div className="px-8 mb-6">
                    <div className="relative mb-4">
                      {/* Track container */}
                      <div className="relative h-6 bg-slate-700 rounded-lg">
                        {/* Progress between current and desired */}
                        <div 
                          className="absolute h-6 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg transition-all duration-300"
                          style={{ 
                            left: `${(currentLevel - 1) / 99 * 100}%`, 
                            width: `${((desiredLevel - currentLevel) / 99) * 100}%` 
                          }}
                        />
                        
                        {/* Current level handle */}
                        <div 
                          className="absolute w-6 h-6 bg-yellow-500 rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-yellow-400"
                          style={{ 
                            left: `${(currentLevel - 1) / 99 * 100}%`, 
                            transform: 'translateX(-50%)',
                            zIndex: 10
                          }}
                        >
                          <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold whitespace-nowrap">
                            Current: {currentLevel}
                          </div>
                        </div>
                        
                        {/* Desired level handle */}
                        <div 
                          className="absolute w-6 h-6 bg-orange-500 rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-orange-400"
                          style={{ 
                            left: `${(desiredLevel - 1) / 99 * 100}%`, 
                            transform: 'translateX(-50%)',
                            zIndex: 10
                          }}
                        >
                          <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-orange-500 text-white px-2 py-1 rounded text-xs font-bold whitespace-nowrap">
                            Target: {desiredLevel}
                          </div>
                        </div>
                      </div>
                      
                      {/* Current Level Slider */}
                      <input
                        type="range"
                        min="1"
                        max={desiredLevel - 1}
                        value={currentLevel}
                        onChange={(e) => {
                          const newCurrent = parseInt(e.target.value);
                          setCurrentLevel(newCurrent);
                        }}
                        className="absolute top-0 w-full h-6 opacity-0 cursor-pointer"
                        style={{ zIndex: 5 }}
                      />
                      
                      {/* Desired Level Slider */}
                      <input
                        type="range"
                        min={currentLevel + 1}
                        max="100"
                        value={desiredLevel}
                        onChange={(e) => {
                          const newDesired = parseInt(e.target.value);
                          setDesiredLevel(newDesired);
                        }}
                        className="absolute top-0 w-full h-6 opacity-0 cursor-pointer"
                        style={{ zIndex: 6 }}
                      />
                    </div>
                    
                    {/* Level markers */}
                    <div className="flex justify-between text-sm text-gray-400 mb-4">
                      <div className="text-center">
                        <div className="font-bold">1</div>
                        <div className="text-xs">START</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold">25</div>
                        <div className="text-xs">LOW</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold">50</div>
                        <div className="text-xs">MID</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold">75</div>
                        <div className="text-xs">HIGH</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold">100</div>
                        <div className="text-xs">MAX</div>
                      </div>
                    </div>
                    
                    {/* Level Range Info */}
                    <div className="p-4 bg-slate-700 rounded-lg">
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <div className="text-yellow-400 font-bold text-lg">{currentLevel}</div>
                          <div className="text-xs text-gray-400">CURRENT</div>
                        </div>
                        <div>
                          <div className="text-blue-400 font-bold text-lg">{desiredLevel - currentLevel}</div>
                          <div className="text-xs text-gray-400">LEVELS</div>
                        </div>
                        <div>
                          <div className="text-orange-400 font-bold text-lg">{desiredLevel}</div>
                          <div className="text-xs text-gray-400">TARGET</div>
                        </div>
                      </div>
                      <div className="mt-3 text-center">
                        <div className="text-green-400 font-bold">
                          {currentLevel} → {desiredLevel} ({desiredLevel - currentLevel} levels)
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Live stats */}
              <div className="mt-8 grid grid-cols-4 gap-4 max-w-3xl mx-auto">
                <div className="bg-slate-700 p-4 rounded-lg text-center">
                  <div className="text-yellow-400 font-bold text-xl">{currentLevel}</div>
                  <div className="text-xs text-gray-400 mt-1">CURRENT</div>
                </div>
                <div className="bg-slate-700 p-4 rounded-lg text-center">
                  <div className="text-orange-400 font-bold text-xl">{desiredLevel}</div>
                  <div className="text-xs text-gray-400 mt-1">TARGET</div>
                </div>
                <div className="bg-slate-700 p-4 rounded-lg text-center">
                  <div className="text-green-400 font-bold text-xl">${totalPrice.toFixed(0)}</div>
                  <div className="text-xs text-gray-400 mt-1">TOTAL COST</div>
                </div>
                <div className="bg-slate-700 p-4 rounded-lg text-center">
                  <div className="text-blue-400 font-bold text-xl">
                    {completionTime.days > 0 ? `${completionTime.days}d` : `${completionTime.hours}h`}
                  </div>
                  <div className="text-xs text-gray-400 mt-1">EST. TIME</div>
                </div>
              </div>
            </div>

            {/* Steps 2, 3, 4 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-lg font-bold mr-3">
                    2
                  </div>
                  <div>
                    <h3 className="font-semibold">ACC SHARED OR PLAY</h3>
                    <h3 className="font-semibold">W BOOSTERS</h3>
                  </div>
                </div>
                <CustomDropDown
                  options={LolLevelBoostingDropDown}
                  selectedValue={accShared}
                  onSelect={setAccShared}
                  placeholder="ACC SHARED"
                />
              </div>

              <div>
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-lg font-bold mr-3">
                    3
                  </div>
                  <h3 className="font-semibold">SELECT ROLE</h3>
                </div>
                <CustomDropDown
                  options={roleOptions}
                  selectedValue={role}
                  onSelect={setRole}
                  placeholder="ANY ROLE"
                />
              </div>

              <div>
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-lg font-bold mr-3">
                    4
                  </div>
                  <h3 className="font-semibold">SELECT PLATFORM</h3>
                </div>
                <CustomDropDown
                  options={platformOptions}
                  selectedValue={platform}
                  onSelect={setPlatform}
                  placeholder="PC"
                />
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="col-span-12 lg:col-span-3 bg-slate-800 rounded-md p-2">
            {/* Level Summary */}
            <div className="bg-slate-700 p-4 rounded-lg mb-6 flex items-center justify-center">
              <div className="text-center">
                <div className="text-sm text-gray-400 mb-2">BOOST FROM LEVEL</div>
                <div className="text-3xl font-bold mb-3">
                  <span className="text-yellow-400">{currentLevel}</span>
                  <span className="text-gray-400 mx-2">→</span>
                  <span className="text-orange-400">{desiredLevel}</span>
                </div>
                <div className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-bold">
                  +{desiredLevel - currentLevel} LEVELS
                </div>
              </div>
            </div>

            {/* Completion Time */}
            <div className="bg-slate-700 p-4 rounded-lg mb-6">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">APPROXIMATE COMPLETION</span>
              </div>
              <div className="text-xl font-bold text-center">
                {completionTime.days > 0 && `${completionTime.days} DAYS `}
                {completionTime.hours} HOURS
              </div>
              <div className="flex justify-between items-center mt-4">
                <span>ACTIVE BOOSTERS</span>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  <span className="font-bold">61</span>
                </div>
              </div>
              
              <div className="mt-4 pt-3 border-t border-gray-600 text-sm text-gray-400">
                <div className="flex justify-between">
                  <span>Level Range:</span>
                  <span className="font-bold text-orange-400">{currentLevel} → {desiredLevel}</span>
                </div>
                <div className="flex justify-between">
                  <span>Levels to Boost:</span>
                  <span className="font-bold text-blue-400">{desiredLevel - currentLevel}</span>
                </div>
                <div className="flex justify-between">
                  <span>Time per Level:</span>
                  <span>{desiredLevel > currentLevel ? ((((completionTime.days * 24) + completionTime.hours) / (desiredLevel - currentLevel)).toFixed(1)) : '0'} hrs</span>
                </div>
              </div>
            </div>

            {/* Add-ons */}
            <div className="space-y-4 mb-6">
              {[
                { label: "Play with Boosters (+40%)", state: boosters, setter: setBoosters },
                { label: "Appear Offline (Free)", state: offline, setter: setOffline },
                { label: "Champions/Roles (Free)", state: roles, setter: setRoles },
                { label: "Priority Order (+20%)", state: priority, setter: setPriority },
                { label: "Stream Games (+10%)", state: stream, setter: setStream },
              ].map((addon, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm text-gray-400">{addon.label}</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={addon.state}
                      onChange={() => addon.setter(!addon.state)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-violet-500 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-violet-600"></div>
                  </label>
                </div>
              ))}
            </div>

            {/* Discount Code */}
            <DiscountCodeComponent setDiscount={setDiscount} discount={discount} />

            {/* Price Summary */}
            <div className="bg-slate-700 p-4 rounded-lg mb-6">
              <div className="flex justify-between items-center text-xl font-bold mb-4">
                <span>Total Amount</span>
                <div className="text-right">
                  {discount < 1 && (
                    <div className="text-sm text-gray-400 line-through mb-1">
                      Original: ${originalPrice.toFixed(2)}
                    </div>
                  )}
                  <div className="text-2xl text-green-400">
                    ${totalPrice.toFixed(2)}
                  </div>
                  {discount < 1 && (
                    <div className="text-sm text-green-400">
                      You save: ${(originalPrice - totalPrice).toFixed(2)}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="pt-4 border-t border-gray-600 text-sm text-gray-400">
                <div className="flex justify-between">
                  <span>Base Price ({currentLevel}→{desiredLevel}):</span>
                  <span>${calculateBasePrice().toFixed(2)}</span>
                </div>
                {discount < 1 && (
                  <>
                    <div className="flex justify-between text-red-400 mt-2">
                      <span>Discount ({Math.round((1 - discount) * 100)}%):</span>
                      <span>-${(originalPrice - totalPrice).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between font-bold text-green-400 text-base mt-2 pt-2 border-t border-gray-600">
                      <span>Final Price:</span>
                      <span>${totalPrice.toFixed(2)}</span>
                    </div>
                  </>
                )}
              </div>
              
              {desiredLevel === 100 && (
                <div className="mt-4 p-3 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-lg border border-orange-500/30">
                  <div className="text-center">
                    <div className="text-orange-400 font-bold text-sm">MAX LEVEL BOOST</div>
                    <div className="text-white text-xs mt-1">
                      Complete {currentLevel}→100 journey in {completionTime.days} days {completionTime.hours} hours!
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Order Button */}
            {loading ? (
              <button
                disabled
                className="w-full bg-gray-600 text-white py-3 rounded-lg flex justify-center items-center"
              >
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                className="w-full bg-violet-600 hover:bg-violet-700 text-white py-3 rounded-lg font-bold text-lg transition-colors"
              >
                Level Up Now
              </button>
            )}

            <div className="text-center text-xs text-gray-400 mt-4">
              Completion Time: {completionTime.days} Days {completionTime.hours} Hours
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LevelBoostingComponent;