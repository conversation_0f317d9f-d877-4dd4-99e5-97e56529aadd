"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/marvel-rivals/[...slug]/page",{

/***/ "(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx":
/*!*******************************************************************!*\
  !*** ./app/marvel-rivals/[...slug]/components/level-boosting.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n// Custom Dropdown Component\nconst CustomDropDown = (param)=>{\n    let { options, placeholder, selectedValue, onSelect, className = \"\" } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-purple text-white px-6 py-3 rounded-lg cursor-pointer flex items-center justify-center min-w-[200px] hover:bg-primary-purple/80 transition-colors border border-secondary-dark\",\n                onClick: ()=>setIsOpen(!isOpen),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium font-montserrat\",\n                        children: (selectedValue === null || selectedValue === void 0 ? void 0 : selectedValue.label) || placeholder\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"ml-2 w-4 h-4\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-full left-0 right-0 bg-primary-dark-grey border border-secondary-dark rounded-lg mt-1 z-50 shadow-lg\",\n                children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 hover:bg-secondary-dark cursor-pointer text-white transition-colors font-montserrat\",\n                        onClick: ()=>{\n                            onSelect(option);\n                            setIsOpen(false);\n                        },\n                        children: option.label\n                    }, option.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomDropDown, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c = CustomDropDown;\n// Discount Code Component\nconst DiscountCodeComponent = (param)=>{\n    let { setDiscount, discount } = param;\n    _s1();\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [applied, setApplied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const applyDiscount = ()=>{\n        if (code.toLowerCase() === \"discount10\") {\n            setDiscount(0.9);\n            setApplied(true);\n        } else if (code.toLowerCase() === \"save25\") {\n            setDiscount(0.75);\n            setApplied(true);\n        } else {\n            alert('Invalid discount code. Try \"discount10\" or \"save25\"');\n        }\n    };\n    const removeDiscount = ()=>{\n        setDiscount(1);\n        setApplied(false);\n        setCode(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-4 p-3 bg-primary-dark-grey rounded-lg border border-secondary-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: code,\n                        onChange: (e)=>setCode(e.target.value),\n                        placeholder: \"Try: discount10 or save25\",\n                        className: \"flex-1 px-3 py-2 bg-secondary-dark border border-secondary-dark rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple font-montserrat\",\n                        disabled: applied\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: applied ? removeDiscount : applyDiscount,\n                        className: \"px-4 py-2 rounded font-medium transition-colors font-montserrat \".concat(applied ? \"bg-red-600 hover:bg-red-700 text-white\" : \"bg-primary-purple hover:bg-primary-purple/80 text-white\"),\n                        children: applied ? \"Remove\" : \"Apply\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            applied && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-green-400 text-sm mt-1 font-montserrat\",\n                children: [\n                    \"Discount applied successfully! \",\n                    Math.round((1 - discount) * 100),\n                    \"% off\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DiscountCodeComponent, \"a7bggQr9ThxF6zYom1UyTK7Cruc=\");\n_c1 = DiscountCodeComponent;\n// Multi-Range Slider Component\nconst MultiRangeSlider = (param)=>{\n    let { min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange } = param;\n    _s2();\n    const sliderRef = useRef(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getPercent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((value)=>Math.round((value - min) / (max - min) * 100), [\n        min,\n        max\n    ]);\n    const handleMouseDown = (type)=>(event)=>{\n            setIsDragging(type);\n            event.preventDefault();\n        };\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        if (!isDragging || !sliderRef.current) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = Math.min(100, Math.max(0, (event.clientX - rect.left) / rect.width * 100));\n        const value = Math.round(min + percent / 100 * (max - min));\n        if (isDragging === \"current\") {\n            const newCurrent = Math.min(value, desiredLevel - 1);\n            onCurrentChange(Math.max(min, newCurrent));\n        } else if (isDragging === \"desired\") {\n            const newDesired = Math.max(value, currentLevel + 1);\n            onDesiredChange(Math.min(max, newDesired));\n        }\n    }, [\n        isDragging,\n        min,\n        max,\n        currentLevel,\n        desiredLevel,\n        onCurrentChange,\n        onDesiredChange\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setIsDragging(null);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    const currentPercent = getPercent(currentLevel);\n    const desiredPercent = getPercent(desiredLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-8 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-4\",\n                ref: sliderRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-6 bg-secondary-dark rounded-lg border border-secondary-dark\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute h-6 bg-gradient-to-r from-primary-yellow to-primary-orange rounded-lg transition-all duration-300\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                width: \"\".concat(desiredPercent - currentPercent, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-6 h-6 bg-primary-yellow rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-primary-yellow/80 z-20\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"current\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-primary-yellow text-black px-2 py-1 rounded text-xs font-bold whitespace-nowrap font-montserrat\",\n                                children: [\n                                    \"Current: \",\n                                    currentLevel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-6 h-6 bg-primary-orange rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-primary-orange/80 z-20\",\n                            style: {\n                                left: \"\".concat(desiredPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"desired\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-primary-orange text-white px-2 py-1 rounded text-xs font-bold whitespace-nowrap font-montserrat\",\n                                children: [\n                                    \"Target: \",\n                                    desiredLevel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between text-sm text-gray-400 mb-4 font-montserrat\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"START\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"25\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"LOW\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"MID\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"75\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"HIGH\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"100\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"MAX\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-primary-dark-grey rounded-lg border border-secondary-dark\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-yellow font-bold text-lg font-montserrat\",\n                                        children: currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"CURRENT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-purple font-bold text-lg font-montserrat\",\n                                        children: desiredLevel - currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"LEVELS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-orange font-bold text-lg font-montserrat\",\n                                        children: desiredLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"TARGET\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-400 font-bold font-montserrat\",\n                            children: [\n                                currentLevel,\n                                \" → \",\n                                desiredLevel,\n                                \" (\",\n                                desiredLevel - currentLevel,\n                                \" levels)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MultiRangeSlider, \"IBff4LxYZyFuRUNRi8ivWheMclo=\");\n_c2 = MultiRangeSlider;\nconst LevelBoostingComponent = ()=>{\n    _s3();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [discount, setDiscount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Level states\n    const [currentLevel, setCurrentLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(21);\n    const [desiredLevel, setDesiredLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(64);\n    // Mock data\n    const LolLevelBoostingDropDown = [\n        {\n            id: \"acc1\",\n            label: \"ACC SHARED\",\n            title: \"ACC SHARED\",\n            value: 0,\n            action: \"None\"\n        },\n        {\n            id: \"acc2\",\n            label: \"PLAY W BOOSTERS (100%)\",\n            title: \"PLAY W BOOSTERS (100%)\",\n            value: 100,\n            action: \"Increase\"\n        },\n        {\n            id: \"acc3\",\n            label: \"PLAY W BOOSTERS (200%)\",\n            title: \"PLAY W BOOSTERS (200%)\",\n            value: 200,\n            action: \"Increase\"\n        }\n    ];\n    const platformOptions = [\n        {\n            id: \"pc\",\n            label: \"PC\",\n            title: \"PC\"\n        },\n        {\n            id: \"console\",\n            label: \"CONSOLE\",\n            title: \"CONSOLE\"\n        }\n    ];\n    const roleOptions = [\n        {\n            id: \"any\",\n            label: \"ANY ROLE\",\n            title: \"ANY ROLE\"\n        },\n        {\n            id: \"tank\",\n            label: \"TANK\",\n            title: \"TANK\"\n        },\n        {\n            id: \"damage\",\n            label: \"DAMAGE\",\n            title: \"DAMAGE\"\n        },\n        {\n            id: \"support\",\n            label: \"SUPPORT\",\n            title: \"SUPPORT\"\n        }\n    ];\n    // Selection states\n    const [accShared, setAccShared] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(LolLevelBoostingDropDown[0]);\n    const [platform, setPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(platformOptions[0]);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(roleOptions[0]);\n    // Price states\n    const [totalPrice, setTotalPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [completionTime, setCompletionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        days: 0,\n        hours: 0\n    });\n    // Add-on states\n    const [boosters, setBoosters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [offline, setOffline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [priority, setPriority] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Level adjustment functions\n    const adjustCurrentLevel = (increment)=>{\n        setCurrentLevel((prev)=>{\n            const newLevel = Math.max(1, Math.min(99, prev + increment));\n            if (newLevel >= desiredLevel) {\n                setDesiredLevel(Math.min(100, newLevel + 1));\n            }\n            return newLevel;\n        });\n    };\n    const adjustDesiredLevel = (increment)=>{\n        setDesiredLevel((prev)=>{\n            const newLevel = Math.max(currentLevel + 1, Math.min(100, prev + increment));\n            return newLevel;\n        });\n    };\n    // Calculate price based on level difference\n    const calculateBasePrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return 5.21;\n        }\n        const maxPrice = 2681.80;\n        const minPrice = 5.21;\n        const priceRange = maxPrice - minPrice;\n        const calculatedPrice = minPrice + (levelDifference - 1) / 98 * priceRange;\n        return Math.max(minPrice, calculatedPrice);\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Calculate completion time\n    const calculateCompletionTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return {\n                days: 0,\n                hours: 1\n            };\n        }\n        const maxDays = 73;\n        const maxHours = 5;\n        const totalMaxHours = maxDays * 24 + maxHours;\n        const totalHours = levelDifference / 99 * totalMaxHours;\n        const days = Math.floor(totalHours / 24);\n        const hours = Math.round(totalHours % 24);\n        return {\n            days,\n            hours\n        };\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Calculate total price with add-ons\n    const calculateTotalPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const basePrice = calculateBasePrice();\n        let additionalPrice = 0;\n        if (accShared.label === \"PLAY W BOOSTERS (100%)\") {\n            additionalPrice += basePrice * 1.0;\n        } else if (accShared.label === \"PLAY W BOOSTERS (200%)\") {\n            additionalPrice += basePrice * 2.0;\n        }\n        if (boosters) additionalPrice += basePrice * 0.4;\n        if (priority) additionalPrice += basePrice * 0.2;\n        if (stream) additionalPrice += basePrice * 0.1;\n        const subtotal = basePrice + additionalPrice;\n        const finalPrice = subtotal * discount;\n        return finalPrice;\n    }, [\n        calculateBasePrice,\n        accShared.label,\n        boosters,\n        priority,\n        stream,\n        discount\n    ]);\n    // Handle order creation\n    const handleSubmit = async ()=>{\n        if (desiredLevel <= currentLevel) {\n            alert(\"Desired level must be higher than current level!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            alert(\"Order created successfully!\\nLevel: \".concat(currentLevel, \" → \").concat(desiredLevel, \"\\nTotal: $\").concat(totalPrice.toFixed(2)));\n        } catch (error) {\n            console.error(\"Order creation failed:\", error);\n            alert(\"Failed to create order. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const time = calculateCompletionTime();\n        setCompletionTime(time);\n    }, [\n        calculateCompletionTime\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const price = calculateTotalPrice();\n        setTotalPrice(price);\n    }, [\n        calculateTotalPrice\n    ]);\n    const originalPrice = calculateTotalPrice() / discount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"col-span-12 bg-primary-dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-12 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-9 bg-primary-dark-grey rounded-md px-6 py-8 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center text-xl font-bold mr-4 text-white\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white font-montserrat\",\n                                                children: \"SELECT YOUR CURRENT AND DESIRED LEVEL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-4xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4\",\n                                                                    children: \"CURRENT LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-gray-300\",\n                                                                    children: currentLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(-1),\n                                                                            className: \"w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center hover:bg-teal-700 transition-colors\",\n                                                                            disabled: currentLevel <= 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 416,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-yellow-500 text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px]\",\n                                                                            children: currentLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(1),\n                                                                            className: \"w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center hover:bg-teal-700 transition-colors\",\n                                                                            disabled: currentLevel >= 99,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 426,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4\",\n                                                                    children: \"DESIRED LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-orange-400\",\n                                                                    children: desiredLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(-1),\n                                                                            className: \"w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center hover:bg-teal-700 transition-colors\",\n                                                                            disabled: desiredLevel <= currentLevel + 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 441,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-orange-500 text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px]\",\n                                                                            children: desiredLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 443,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(1),\n                                                                            className: \"w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center hover:bg-teal-700 transition-colors\",\n                                                                            disabled: desiredLevel >= 100,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 451,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-8 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative h-6 bg-slate-700 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute h-6 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg transition-all duration-300\",\n                                                                            style: {\n                                                                                left: \"\".concat((currentLevel - 1) / 99 * 100, \"%\"),\n                                                                                width: \"\".concat((desiredLevel - currentLevel) / 99 * 100, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 463,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute w-6 h-6 bg-yellow-500 rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-yellow-400\",\n                                                                            style: {\n                                                                                left: \"\".concat((currentLevel - 1) / 99 * 100, \"%\"),\n                                                                                transform: \"translateX(-50%)\",\n                                                                                zIndex: 10\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold whitespace-nowrap\",\n                                                                                children: [\n                                                                                    \"Current: \",\n                                                                                    currentLevel\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 480,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute w-6 h-6 bg-orange-500 rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-orange-400\",\n                                                                            style: {\n                                                                                left: \"\".concat((desiredLevel - 1) / 99 * 100, \"%\"),\n                                                                                transform: \"translateX(-50%)\",\n                                                                                zIndex: 10\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-orange-500 text-white px-2 py-1 rounded text-xs font-bold whitespace-nowrap\",\n                                                                                children: [\n                                                                                    \"Target: \",\n                                                                                    desiredLevel\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 486,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"range\",\n                                                                    min: \"1\",\n                                                                    max: desiredLevel - 1,\n                                                                    value: currentLevel,\n                                                                    onChange: (e)=>{\n                                                                        const newCurrent = parseInt(e.target.value);\n                                                                        setCurrentLevel(newCurrent);\n                                                                    },\n                                                                    className: \"absolute top-0 w-full h-6 opacity-0 cursor-pointer\",\n                                                                    style: {\n                                                                        zIndex: 5\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"range\",\n                                                                    min: currentLevel + 1,\n                                                                    max: \"100\",\n                                                                    value: desiredLevel,\n                                                                    onChange: (e)=>{\n                                                                        const newDesired = parseInt(e.target.value);\n                                                                        setDesiredLevel(newDesired);\n                                                                    },\n                                                                    className: \"absolute top-0 w-full h-6 opacity-0 cursor-pointer\",\n                                                                    style: {\n                                                                        zIndex: 6\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm text-gray-400 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-bold\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs\",\n                                                                            children: \"START\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-bold\",\n                                                                            children: \"25\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 536,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs\",\n                                                                            children: \"LOW\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-bold\",\n                                                                            children: \"50\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 540,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs\",\n                                                                            children: \"MID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-bold\",\n                                                                            children: \"75\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 544,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs\",\n                                                                            children: \"HIGH\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 545,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-bold\",\n                                                                            children: \"100\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs\",\n                                                                            children: \"MAX\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-slate-700 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-3 gap-4 text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-yellow-400 font-bold text-lg\",\n                                                                                    children: currentLevel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                    lineNumber: 557,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-400\",\n                                                                                    children: \"CURRENT\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                    lineNumber: 558,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-blue-400 font-bold text-lg\",\n                                                                                    children: desiredLevel - currentLevel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                    lineNumber: 561,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-400\",\n                                                                                    children: \"LEVELS\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                    lineNumber: 562,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 560,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-orange-400 font-bold text-lg\",\n                                                                                    children: desiredLevel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                    lineNumber: 565,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-400\",\n                                                                                    children: \"TARGET\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                    lineNumber: 566,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 text-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-green-400 font-bold\",\n                                                                        children: [\n                                                                            currentLevel,\n                                                                            \" → \",\n                                                                            desiredLevel,\n                                                                            \" (\",\n                                                                            desiredLevel - currentLevel,\n                                                                            \" levels)\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 grid grid-cols-4 gap-4 max-w-3xl mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-slate-700 p-4 rounded-lg text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-yellow-400 font-bold text-xl\",\n                                                        children: currentLevel\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: \"CURRENT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-slate-700 p-4 rounded-lg text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-orange-400 font-bold text-xl\",\n                                                        children: desiredLevel\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: \"TARGET\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-slate-700 p-4 rounded-lg text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-400 font-bold text-xl\",\n                                                        children: [\n                                                            \"$\",\n                                                            totalPrice.toFixed(0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: \"TOTAL COST\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-slate-700 p-4 rounded-lg text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-400 font-bold text-xl\",\n                                                        children: completionTime.days > 0 ? \"\".concat(completionTime.days, \"d\") : \"\".concat(completionTime.hours, \"h\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: \"EST. TIME\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-lg font-bold mr-3\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"ACC SHARED OR PLAY\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"W BOOSTERS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomDropDown, {\n                                                options: LolLevelBoostingDropDown,\n                                                selectedValue: accShared,\n                                                onSelect: setAccShared,\n                                                placeholder: \"ACC SHARED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-lg font-bold mr-3\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold\",\n                                                        children: \"SELECT ROLE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomDropDown, {\n                                                options: roleOptions,\n                                                selectedValue: role,\n                                                onSelect: setRole,\n                                                placeholder: \"ANY ROLE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-lg font-bold mr-3\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold\",\n                                                        children: \"SELECT PLATFORM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomDropDown, {\n                                                options: platformOptions,\n                                                selectedValue: platform,\n                                                onSelect: setPlatform,\n                                                placeholder: \"PC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-3 bg-slate-800 rounded-md p-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-700 p-4 rounded-lg mb-6 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400 mb-2\",\n                                            children: \"BOOST FROM LEVEL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-400\",\n                                                    children: currentLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 mx-2\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-orange-400\",\n                                                    children: desiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-bold\",\n                                            children: [\n                                                \"+\",\n                                                desiredLevel - currentLevel,\n                                                \" LEVELS\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-700 p-4 rounded-lg mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"APPROXIMATE COMPLETION\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xl font-bold text-center\",\n                                        children: [\n                                            completionTime.days > 0 && \"\".concat(completionTime.days, \" DAYS \"),\n                                            completionTime.hours,\n                                            \" HOURS\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"ACTIVE BOOSTERS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold\",\n                                                        children: \"61\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 pt-3 border-t border-gray-600 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Level Range:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-orange-400\",\n                                                        children: [\n                                                            currentLevel,\n                                                            \" → \",\n                                                            desiredLevel\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Levels to Boost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-blue-400\",\n                                                        children: desiredLevel - currentLevel\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Time per Level:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            desiredLevel > currentLevel ? ((completionTime.days * 24 + completionTime.hours) / (desiredLevel - currentLevel)).toFixed(1) : \"0\",\n                                                            \" hrs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 mb-6\",\n                                children: [\n                                    {\n                                        label: \"Play with Boosters (+40%)\",\n                                        state: boosters,\n                                        setter: setBoosters\n                                    },\n                                    {\n                                        label: \"Appear Offline (Free)\",\n                                        state: offline,\n                                        setter: setOffline\n                                    },\n                                    {\n                                        label: \"Champions/Roles (Free)\",\n                                        state: roles,\n                                        setter: setRoles\n                                    },\n                                    {\n                                        label: \"Priority Order (+20%)\",\n                                        state: priority,\n                                        setter: setPriority\n                                    },\n                                    {\n                                        label: \"Stream Games (+10%)\",\n                                        state: stream,\n                                        setter: setStream\n                                    }\n                                ].map((addon, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: addon.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: addon.state,\n                                                        onChange: ()=>addon.setter(!addon.state),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-violet-500 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-violet-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 705,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DiscountCodeComponent, {\n                                setDiscount: setDiscount,\n                                discount: discount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 729,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-700 p-4 rounded-lg mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center text-xl font-bold mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Total Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    discount < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400 line-through mb-1\",\n                                                        children: [\n                                                            \"Original: $\",\n                                                            originalPrice.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl text-green-400\",\n                                                        children: [\n                                                            \"$\",\n                                                            totalPrice.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    discount < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-green-400\",\n                                                        children: [\n                                                            \"You save: $\",\n                                                            (originalPrice - totalPrice).toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 745,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-4 border-t border-gray-600 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Base Price (\",\n                                                            currentLevel,\n                                                            \"→\",\n                                                            desiredLevel,\n                                                            \"):\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateBasePrice().toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            discount < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-red-400 mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Discount (\",\n                                                                    Math.round((1 - discount) * 100),\n                                                                    \"%):\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"-$\",\n                                                                    (originalPrice - totalPrice).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between font-bold text-green-400 text-base mt-2 pt-2 border-t border-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Final Price:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    totalPrice.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    desiredLevel === 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-lg border border-orange-500/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-orange-400 font-bold text-sm\",\n                                                    children: \"MAX LEVEL BOOST\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white text-xs mt-1\",\n                                                    children: [\n                                                        \"Complete \",\n                                                        currentLevel,\n                                                        \"→100 journey in \",\n                                                        completionTime.days,\n                                                        \" days \",\n                                                        completionTime.hours,\n                                                        \" hours!\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 775,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 772,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 13\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                disabled: true,\n                                className: \"w-full bg-gray-600 text-white py-3 rounded-lg flex justify-center items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 789,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 785,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSubmit,\n                                className: \"w-full bg-violet-600 hover:bg-violet-700 text-white py-3 rounded-lg font-bold text-lg transition-colors\",\n                                children: \"Level Up Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-xs text-gray-400 mt-4\",\n                                children: [\n                                    \"Completion Time: \",\n                                    completionTime.days,\n                                    \" Days \",\n                                    completionTime.hours,\n                                    \" Hours\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 800,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 390,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n            lineNumber: 389,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 388,\n        columnNumber: 5\n    }, undefined);\n};\n_s3(LevelBoostingComponent, \"I4xZ2l8//fbKv/5I/JTXl5HJEuo=\");\n_c3 = LevelBoostingComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LevelBoostingComponent);\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"CustomDropDown\");\n$RefreshReg$(_c1, \"DiscountCodeComponent\");\n$RefreshReg$(_c2, \"MultiRangeSlider\");\n$RefreshReg$(_c3, \"LevelBoostingComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx\n"));

/***/ })

});