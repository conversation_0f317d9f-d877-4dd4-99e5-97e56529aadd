"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/marvel-rivals/[...slug]/page",{

/***/ "(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx":
/*!*******************************************************************!*\
  !*** ./app/marvel-rivals/[...slug]/components/level-boosting.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_discountCode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/discountCode */ \"(app-pages-browser)/./components/discountCode.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-loader-spinner */ \"(app-pages-browser)/./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _constants_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/common */ \"(app-pages-browser)/./constants/common.ts\");\n/* harmony import */ var _constants_service_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/constants/service-types */ \"(app-pages-browser)/./constants/service-types.ts\");\n/* harmony import */ var _database_functions_database__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/database/functions.database */ \"(app-pages-browser)/./database/functions.database.ts\");\n/* harmony import */ var _utils_now_payments__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/now-payments */ \"(app-pages-browser)/./utils/now-payments.ts\");\n/* harmony import */ var _utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/toast-utils */ \"(app-pages-browser)/./utils/toast-utils.ts\");\n/* harmony import */ var _components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom-dropdown */ \"(app-pages-browser)/./components/custom-dropdown.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst MultiRangeSlider = (param)=>{\n    let { min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange } = param;\n    _s();\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    // Level markers for the slider\n    const levelMarkers = [\n        1,\n        10,\n        20,\n        30,\n        40,\n        50,\n        60,\n        70,\n        80,\n        90,\n        100\n    ];\n    const getPercent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        return (value - min) / (max - min) * 100;\n    }, [\n        min,\n        max\n    ]);\n    const getValueFromPercent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((percent)=>{\n        return Math.round(min + percent / 100 * (max - min));\n    }, [\n        min,\n        max\n    ]);\n    const handleMouseDown = (type)=>(event)=>{\n            if (!sliderRef.current) return;\n            setIsDragging(type);\n            const rect = sliderRef.current.getBoundingClientRect();\n            const handlePosition = type === \"current\" ? getPercent(currentLevel) : getPercent(desiredLevel);\n            const clickPosition = (event.clientX - rect.left) / rect.width * 100;\n            setDragOffset(clickPosition - handlePosition);\n            event.preventDefault();\n        };\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((event)=>{\n        if (!isDragging || !sliderRef.current) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = Math.min(100, Math.max(0, (event.clientX - rect.left) / rect.width * 100 - dragOffset));\n        const value = getValueFromPercent(percent);\n        if (isDragging === \"current\") {\n            const newCurrent = Math.min(Math.max(min, value), desiredLevel - 1);\n            onCurrentChange(newCurrent);\n        } else if (isDragging === \"desired\") {\n            const newDesired = Math.max(Math.min(max, value), currentLevel + 1);\n            onDesiredChange(newDesired);\n        }\n    }, [\n        isDragging,\n        min,\n        max,\n        currentLevel,\n        desiredLevel,\n        onCurrentChange,\n        onDesiredChange,\n        dragOffset,\n        getValueFromPercent\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        setIsDragging(null);\n        setDragOffset(0);\n    }, []);\n    const handleTrackClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((event)=>{\n        if (!sliderRef.current || isDragging) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = (event.clientX - rect.left) / rect.width * 100;\n        const value = getValueFromPercent(percent);\n        const currentPercent = getPercent(currentLevel);\n        const desiredPercent = getPercent(desiredLevel);\n        const clickPercent = percent;\n        // Determine which handle is closer to the click\n        const distanceToCurrent = Math.abs(clickPercent - currentPercent);\n        const distanceToDesired = Math.abs(clickPercent - desiredPercent);\n        if (distanceToCurrent < distanceToDesired) {\n            // Move current level handle\n            const newCurrent = Math.min(Math.max(min, value), desiredLevel - 1);\n            onCurrentChange(newCurrent);\n        } else {\n            // Move desired level handle\n            const newDesired = Math.max(Math.min(max, value), currentLevel + 1);\n            onDesiredChange(newDesired);\n        }\n    }, [\n        currentLevel,\n        desiredLevel,\n        min,\n        max,\n        onCurrentChange,\n        onDesiredChange,\n        getPercent,\n        getValueFromPercent,\n        isDragging\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    const currentPercent = getPercent(currentLevel);\n    const desiredPercent = getPercent(desiredLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-8 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-6\",\n                ref: sliderRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-8 bg-secondary-dark rounded-lg border border-secondary-dark cursor-pointer\",\n                    onClick: handleTrackClick,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute h-8 bg-gradient-to-r from-primary-yellow to-primary-orange rounded-lg transition-all duration-200 shadow-inner\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                width: \"\".concat(Math.max(0, desiredPercent - currentPercent), \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined),\n                        levelMarkers.map((level)=>{\n                            const percent = getPercent(level);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute w-0.5 h-8 bg-gray-500 opacity-50\",\n                                style: {\n                                    left: \"\".concat(percent, \"%\")\n                                }\n                            }, level, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, undefined);\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-8 h-8 bg-primary-yellow rounded-full border-3 border-white shadow-lg transition-all duration-200 cursor-grab z-30 flex items-center justify-center \".concat(isDragging === \"current\" ? \"cursor-grabbing scale-110\" : \"hover:scale-105\"),\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"current\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-black rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-primary-yellow text-black px-3 py-1 rounded-lg text-xs font-bold whitespace-nowrap font-montserrat shadow-lg\",\n                                    children: [\n                                        \"Current: \",\n                                        currentLevel\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-8 h-8 bg-primary-orange rounded-full border-3 border-white shadow-lg transition-all duration-200 cursor-grab z-30 flex items-center justify-center \".concat(isDragging === \"desired\" ? \"cursor-grabbing scale-110\" : \"hover:scale-105\"),\n                            style: {\n                                left: \"\".concat(desiredPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"desired\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-white rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-primary-orange text-white px-3 py-1 rounded-lg text-xs font-bold whitespace-nowrap font-montserrat shadow-lg\",\n                                    children: [\n                                        \"Target: \",\n                                        desiredLevel\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-4 bg-red-text\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between text-sm text-gray-400 font-montserrat\",\n                    children: levelMarkers.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold text-white \".concat(level === 1 ? \"text-end\" : \"\", \" \"),\n                                    children: level\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, undefined),\n                                level === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"START\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 31\n                                }, undefined),\n                                level === 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"MAX\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, level, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-primary-dark-grey rounded-lg border border-secondary-dark\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-yellow font-bold text-lg font-montserrat\",\n                                        children: currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"CURRENT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-purple font-bold text-lg font-montserrat\",\n                                        children: desiredLevel - currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"LEVELS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-orange font-bold text-lg font-montserrat\",\n                                        children: desiredLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"TARGET\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-400 font-bold font-montserrat\",\n                            children: [\n                                currentLevel,\n                                \" → \",\n                                desiredLevel,\n                                \" (\",\n                                desiredLevel - currentLevel,\n                                \" levels)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MultiRangeSlider, \"L/w+XM8UMMuRGYfWugmZVIgU860=\");\n_c = MultiRangeSlider;\nconst LevelBoostingComponent = (param)=>{\n    let { priceControl } = param;\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [discount, setDiscount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    // Level states\n    const [currentLevel, setCurrentLevel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(21);\n    const [desiredLevel, setDesiredLevel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(64);\n    // Mock data\n    const LolLevelBoostingDropDown = [\n        {\n            id: \"acc1\",\n            label: \"ACC SHARED\",\n            title: \"ACC SHARED\",\n            value: 0,\n            action: \"None\"\n        },\n        {\n            id: \"acc2\",\n            label: \"PLAY W BOOSTERS (100%)\",\n            title: \"PLAY W BOOSTERS (100%)\",\n            value: 100,\n            action: \"Increase\"\n        },\n        {\n            id: \"acc3\",\n            label: \"PLAY W BOOSTERS (200%)\",\n            title: \"PLAY W BOOSTERS (200%)\",\n            value: 200,\n            action: \"Increase\"\n        }\n    ];\n    const platformOptions = [\n        {\n            id: \"pc\",\n            label: \"PC\",\n            title: \"PC\"\n        },\n        {\n            id: \"console\",\n            label: \"CONSOLE\",\n            title: \"CONSOLE\"\n        }\n    ];\n    const roleOptions = [\n        {\n            id: \"any\",\n            label: \"ANY ROLE\",\n            title: \"ANY ROLE\"\n        },\n        {\n            id: \"tank\",\n            label: \"TANK\",\n            title: \"TANK\"\n        },\n        {\n            id: \"damage\",\n            label: \"DAMAGE\",\n            title: \"DAMAGE\"\n        },\n        {\n            id: \"support\",\n            label: \"SUPPORT\",\n            title: \"SUPPORT\"\n        }\n    ];\n    // Selection states\n    const [accShared, setAccShared] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [platform, setPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Price states\n    const [basePrice, setBasePrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [accSharedPrice, setAccSharedPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [boostersPrice, setBoostersPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [priorityPrice, setPriorityPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [streamPrice, setStreamPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [completionTime, setCompletionTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        days: 0,\n        hours: 0\n    });\n    const [total, setTotal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        price: \"5.21\"\n    });\n    const [priceCt, setPriceCt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    // Add-on states\n    const [boosters, setBoosters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [offline, setOffline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [priority, setPriority] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Level adjustment functions\n    const adjustCurrentLevel = (increment)=>{\n        setCurrentLevel((prev)=>{\n            const newLevel = Math.max(1, Math.min(99, prev + increment));\n            if (newLevel >= desiredLevel) {\n                setDesiredLevel(Math.min(100, newLevel + 1));\n            }\n            return newLevel;\n        });\n    };\n    const adjustDesiredLevel = (increment)=>{\n        setDesiredLevel((prev)=>{\n            const newLevel = Math.max(currentLevel + 1, Math.min(100, prev + increment));\n            return newLevel;\n        });\n    };\n    // Calculate price based on level difference\n    const calculateBasePrice = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return 5.21;\n        }\n        const maxPrice = 2681.80;\n        const minPrice = 5.21;\n        const priceRange = maxPrice - minPrice;\n        const calculatedPrice = minPrice + (levelDifference - 1) / 98 * priceRange;\n        return Math.max(minPrice, calculatedPrice);\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Calculate completion time\n    const calculateCompletionTime = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return {\n                days: 0,\n                hours: 1\n            };\n        }\n        const maxDays = 73;\n        const maxHours = 5;\n        const totalMaxHours = maxDays * 24 + maxHours;\n        const totalHours = levelDifference / 99 * totalMaxHours;\n        const days = Math.floor(totalHours / 24);\n        const hours = Math.round(totalHours % 24);\n        return {\n            days,\n            hours\n        };\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Handle ACC SHARED selection\n    const handleAccSharedChange = (option)=>{\n        setAccShared(option);\n        if (option.label === \"PLAY W BOOSTERS (100%)\") {\n            setAccSharedPrice(basePrice * 1.0);\n        } else if (option.label === \"PLAY W BOOSTERS (200%)\") {\n            setAccSharedPrice(basePrice * 2.0);\n        } else {\n            setAccSharedPrice(0);\n        }\n    };\n    // Handle addon changes with proper price calculation\n    const handleAddonChange = (isEnabled, percentage, setPriceFunction)=>{\n        if (isEnabled) {\n            setPriceFunction(basePrice * percentage);\n        } else {\n            setPriceFunction(0);\n        }\n    };\n    // Calculate total price\n    const updateTotalPrice = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        const subtotal = basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice;\n        const finalPrice = subtotal * discount;\n        if (priceCt && Object.keys(priceCt).length > 0) {\n            try {\n                const result = (0,_utils_now_payments__WEBPACK_IMPORTED_MODULE_8__.calcTotalPricePrecentage)(priceCt, finalPrice);\n                setTotal(result);\n            } catch (error) {\n                console.error(\"Price calculation error:\", error);\n                setTotal({\n                    price: finalPrice.toFixed(2)\n                });\n            }\n        } else {\n            setTotal({\n                price: finalPrice.toFixed(2)\n            });\n        }\n    }, [\n        basePrice,\n        accSharedPrice,\n        boostersPrice,\n        priorityPrice,\n        streamPrice,\n        discount,\n        priceCt\n    ]);\n    // Handle order creation with proper validation and error handling\n    const handleSubmit = async ()=>{\n        // Validation\n        if (desiredLevel <= currentLevel) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Desired level must be higher than current level!\");\n            return;\n        }\n        if (!accShared) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Please select account sharing option!\");\n            return;\n        }\n        if (!platform) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Please select a platform!\");\n            return;\n        }\n        if (!role) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Please select a role!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const orderId = await (0,_database_functions_database__WEBPACK_IMPORTED_MODULE_7__.createOrder)({\n                game_name: _constants_common__WEBPACK_IMPORTED_MODULE_5__.GameNames.LeagueOfLegends,\n                service_type: _constants_service_types__WEBPACK_IMPORTED_MODULE_6__.MarvelRivalsServiceTypes.LevelBoosting,\n                order_details: JSON.stringify({\n                    currentLevel,\n                    desiredLevel,\n                    levelDifference: desiredLevel - currentLevel,\n                    platform: platform.label,\n                    role: role.label,\n                    accShared: accShared.label,\n                    addons: {\n                        boosters,\n                        offline,\n                        roles,\n                        priority,\n                        stream\n                    }\n                }),\n                play_with_boosters: boosters,\n                appear_offline: offline,\n                champions_roles: roles,\n                priority_order: priority,\n                stream_game: stream,\n                completion_time_hours: completionTime.days * 24 + completionTime.hours,\n                price: parseFloat(total === null || total === void 0 ? void 0 : total.price) || basePrice\n            });\n            if (orderId) {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.successToast)(\"Order created successfully!\");\n                router.push(\"/checkout?order_id=\".concat(orderId));\n            } else {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Failed to create order. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Order creation failed:\", error);\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Failed to create order. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Get price control\n    const getPriceControl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(async ()=>{\n        try {\n            setPriceCt(priceControl || {});\n        } catch (error) {\n            console.error(\"Price control error:\", error);\n            setPriceCt({});\n        }\n    }, [\n        priceControl\n    ]);\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const newPrice = calculateBasePrice();\n        setBasePrice(newPrice);\n        const newTime = calculateCompletionTime();\n        setCompletionTime(newTime);\n    }, [\n        calculateBasePrice,\n        calculateCompletionTime\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        updateTotalPrice();\n    }, [\n        updateTotalPrice\n    ]);\n    // Update prices when base price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (accShared) {\n            handleAccSharedChange(accShared);\n        }\n    }, [\n        basePrice\n    ]);\n    // Handle addon price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        handleAddonChange(boosters, 0.4, setBoostersPrice);\n    }, [\n        boosters,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        handleAddonChange(priority, 0.2, setPriorityPrice);\n    }, [\n        priority,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        handleAddonChange(stream, 0.1, setStreamPrice);\n    }, [\n        stream,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getPriceControl();\n    }, [\n        getPriceControl\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"col-span-12 \",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-12 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-9 bg-primary-dark rounded-md px-6 py-8 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center text-xl font-bold mr-4 text-white\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white font-montserrat\",\n                                                children: \"SELECT YOUR CURRENT AND DESIRED LEVEL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-4xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"CURRENT LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-yellow font-montserrat\",\n                                                                    children: currentLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel <= 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 528,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 523,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-yellow text-black px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: currentLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel >= 99,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 538,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"DESIRED LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-orange font-montserrat\",\n                                                                    children: desiredLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel <= currentLevel + 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 553,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-orange text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: desiredLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 555,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel >= 100,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 563,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiRangeSlider, {\n                                                    min: 1,\n                                                    max: 100,\n                                                    currentLevel: currentLevel,\n                                                    desiredLevel: desiredLevel,\n                                                    onCurrentChange: setCurrentLevel,\n                                                    onDesiredChange: setDesiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid items-center grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"ACC SHARED OR PLAY\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"W BOOSTERS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                options: LolLevelBoostingDropDown,\n                                                selectedValue: accShared,\n                                                onSelect: handleAccSharedChange,\n                                                placeholder: \"ACC SHARED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT ROLE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                options: roleOptions,\n                                                selectedValue: role,\n                                                onSelect: setRole,\n                                                placeholder: \"ANY ROLE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT PLATFORM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                options: platformOptions,\n                                                selectedValue: platform,\n                                                onSelect: setPlatform,\n                                                placeholder: \"PC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-3 bg-primary-dark rounded-md p-2 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-secondary-dark p-4 rounded-lg mb-6 flex items-center justify-center border border-secondary-dark\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400 mb-2 font-montserrat\",\n                                            children: \"BOOST FROM LEVEL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold mb-3 font-montserrat\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-yellow\",\n                                                    children: currentLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 mx-2\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-orange\",\n                                                    children: desiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-primary-yellow to-primary-orange text-white px-4 py-2 rounded-full text-sm font-bold font-montserrat\",\n                                            children: [\n                                                \"+\",\n                                                desiredLevel - currentLevel,\n                                                \" LEVELS\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Play with Boosters (+40%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: boosters,\n                                                        onChange: ()=>setBoosters(!boosters),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Appear Offline (Free)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: offline,\n                                                        onChange: ()=>setOffline(!offline),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Champions/Roles (Free)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: roles,\n                                                        onChange: ()=>setRoles(!roles),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Priority Order (+20%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: priority,\n                                                        onChange: ()=>setPriority(!priority),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Stream Games (+10%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: stream,\n                                                        onChange: ()=>setStream(!stream),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_discountCode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                setDiscount: setDiscount,\n                                discount: discount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 720,\n                                columnNumber: 13\n                            }, undefined),\n                            discount < 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-medium text-gray-400\",\n                                                children: \"Original Price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-medium text-gray-400 line-through\",\n                                                children: [\n                                                    \"$\",\n                                                    (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-sm border border-green-500 p-3 flex justify-between items-center bg-[#1C1E2B]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-semibold text-green-400\",\n                                                children: [\n                                                    \"Total Amount (\",\n                                                    Math.round((1 - discount) * 100),\n                                                    \"% OFF)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-semibold text-green-400\",\n                                                children: [\n                                                    \"$\",\n                                                    (total === null || total === void 0 ? void 0 : total.price) || ((basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice) * discount).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 724,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-montserrat font-semibold\",\n                                        children: \"Total Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-montserrat font-semibold\",\n                                        children: [\n                                            \"$\",\n                                            (total === null || total === void 0 ? void 0 : total.price) || (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 15\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                disabled: true,\n                                type: \"button\",\n                                className: \"flex justify-center items-center text-white bg-secondary-600 font-medium w-full rounded-lg text-sm me-2 mb-2 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_3__.Bars, {\n                                    height: \"40\",\n                                    color: \"white\",\n                                    ariaLabel: \"bars-loading\",\n                                    wrapperStyle: {},\n                                    wrapperClass: \"\",\n                                    visible: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 752,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleSubmit,\n                                className: \"text-white bg-primary-purple hover:bg-primary-shaded-purple focus:ring-4 w-full focus:ring-blue-300 font-medium rounded-lg text-sm px-7 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\",\n                                children: \"Level Up Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 767,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-xs text-gray-400 mt-4 font-montserrat\",\n                                children: [\n                                    \"Completion Time: \",\n                                    completionTime.days,\n                                    \" Days \",\n                                    completionTime.hours,\n                                    \" Hours\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 777,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n            lineNumber: 501,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 500,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(LevelBoostingComponent, \"6QjUyUuzW16cIsH+qP6AcXp4LIs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c1 = LevelBoostingComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LevelBoostingComponent);\nvar _c, _c1;\n$RefreshReg$(_c, \"MultiRangeSlider\");\n$RefreshReg$(_c1, \"LevelBoostingComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx\n"));

/***/ })

});