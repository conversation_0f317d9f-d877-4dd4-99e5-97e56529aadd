import { Minus, Plus } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { CustomDropDown, DiscountCodeComponent, MultiRangeSlider } from './level-boosting-clean';

// Types
interface DropdownOption {
  id: string;
  label: string;
  title: string;
  value?: number;
  action?: string;
}

interface LevelBoostingComponentProps {
  priceControl?: any;
}

const LevelBoostingComponent: React.FC<LevelBoostingComponentProps> = ({ priceControl }) => {
  const [loading, setLoading] = useState(false);
  const [discount, setDiscount] = useState(1);

  // Level states
  const [currentLevel, setCurrentLevel] = useState(21);
  const [desiredLevel, setDesiredLevel] = useState(64);
  
  // Mock data
  const LolLevelBoostingDropDown: DropdownOption[] = [
    { id: "acc1", label: "ACC SHARED", title: "ACC SHARED", value: 0, action: "None" },
    { id: "acc2", label: "PLAY W BOOSTERS (100%)", title: "PLAY W BOOSTERS (100%)", value: 100, action: "Increase" },
    { id: "acc3", label: "PLAY W BOOSTERS (200%)", title: "PLAY W BOOSTERS (200%)", value: 200, action: "Increase" },
  ];

  const platformOptions: DropdownOption[] = [
    { id: "pc", label: "PC", title: "PC" },
    { id: "console", label: "CONSOLE", title: "CONSOLE" },
  ];

  const roleOptions: DropdownOption[] = [
    { id: "any", label: "ANY ROLE", title: "ANY ROLE" },
    { id: "tank", label: "TANK", title: "TANK" },
    { id: "damage", label: "DAMAGE", title: "DAMAGE" },
    { id: "support", label: "SUPPORT", title: "SUPPORT" },
  ];

  // Selection states
  const [accShared, setAccShared] = useState<DropdownOption>(LolLevelBoostingDropDown[0]);
  const [platform, setPlatform] = useState<DropdownOption>(platformOptions[0]);
  const [role, setRole] = useState<DropdownOption>(roleOptions[0]);
  
  // Add-on states
  const [boosters, setBoosters] = useState(false);
  const [offline, setOffline] = useState(false);
  const [roles, setRoles] = useState(false);
  const [priority, setPriority] = useState(false);
  const [stream, setStream] = useState(false);

  // Price states
  const [totalPrice, setTotalPrice] = useState(0);
  const [completionTime, setCompletionTime] = useState({ days: 0, hours: 0 });

  // Level adjustment functions
  const adjustCurrentLevel = (increment: number) => {
    setCurrentLevel(prev => {
      const newLevel = Math.max(1, Math.min(99, prev + increment));
      if (newLevel >= desiredLevel) {
        setDesiredLevel(Math.min(100, newLevel + 1));
      }
      return newLevel;
    });
  };

  const adjustDesiredLevel = (increment: number) => {
    setDesiredLevel(prev => {
      const newLevel = Math.max(currentLevel + 1, Math.min(100, prev + increment));
      return newLevel;
    });
  };

  // Calculate price based on level difference
  const calculateBasePrice = useCallback(() => {
    const levelDifference = desiredLevel - currentLevel;
    if (levelDifference <= 0) {
      return 5.21;
    }
    
    const maxPrice = 2681.80;
    const minPrice = 5.21;
    const priceRange = maxPrice - minPrice;
    
    const calculatedPrice = minPrice + ((levelDifference - 1) / 98) * priceRange;
    
    return Math.max(minPrice, calculatedPrice);
  }, [currentLevel, desiredLevel]);

  // Calculate completion time
  const calculateCompletionTime = useCallback(() => {
    const levelDifference = desiredLevel - currentLevel;
    if (levelDifference <= 0) {
      return { days: 0, hours: 1 };
    }
    
    const maxDays = 73;
    const maxHours = 5;
    const totalMaxHours = (maxDays * 24) + maxHours;
    
    const totalHours = (levelDifference / 99) * totalMaxHours;
    
    const days = Math.floor(totalHours / 24);
    const hours = Math.round(totalHours % 24);
    
    return { days, hours };
  }, [currentLevel, desiredLevel]);

  // Calculate total price with add-ons
  const calculateTotalPrice = useCallback(() => {
    const basePrice = calculateBasePrice();
    let additionalPrice = 0;

    if (accShared.label === "PLAY W BOOSTERS (100%)") {
      additionalPrice += basePrice * 1.0;
    } else if (accShared.label === "PLAY W BOOSTERS (200%)") {
      additionalPrice += basePrice * 2.0;
    }

    if (boosters) additionalPrice += basePrice * 0.4;
    if (priority) additionalPrice += basePrice * 0.2;
    if (stream) additionalPrice += basePrice * 0.1;

    const subtotal = basePrice + additionalPrice;
    const finalPrice = subtotal * discount;
    
    return finalPrice;
  }, [calculateBasePrice, accShared.label, boosters, priority, stream, discount]);

  // Handle order creation
  const handleSubmit = async () => {
    if (desiredLevel <= currentLevel) {
      alert("Desired level must be higher than current level!");
      return;
    }
    
    setLoading(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert(`Order created successfully!\nLevel: ${currentLevel} → ${desiredLevel}\nTotal: $${totalPrice.toFixed(2)}`);
    } catch (error) {
      console.error('Order creation failed:', error);
      alert("Failed to create order. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    const time = calculateCompletionTime();
    setCompletionTime(time);
  }, [calculateCompletionTime]);

  useEffect(() => {
    const price = calculateTotalPrice();
    setTotalPrice(price);
  }, [calculateTotalPrice]);

  const originalPrice = calculateTotalPrice() / discount;

  return (
    <div className="bg-primary-dark text-white min-h-screen p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-primary-dark-grey rounded-lg border border-secondary-dark p-6">
          <h1 className="text-3xl font-bold text-center mb-8 text-primary-yellow font-montserrat">
            MARVEL RIVALS LEVEL BOOSTING
          </h1>

          {/* Level Selection */}
          <div className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-primary-orange font-montserrat">SELECT LEVELS</h2>
            
            {/* Multi-Range Slider */}
            <MultiRangeSlider
              min={1}
              max={100}
              currentLevel={currentLevel}
              desiredLevel={desiredLevel}
              onCurrentChange={setCurrentLevel}
              onDesiredChange={setDesiredLevel}
            />

            {/* Manual Level Adjustments */}
            <div className="grid grid-cols-2 gap-6 mb-6">
              <div className="bg-primary-dark-grey p-4 rounded-lg border border-secondary-dark">
                <label className="block text-sm font-medium mb-2 text-gray-300 font-montserrat">Current Level</label>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => adjustCurrentLevel(-1)}
                    className="bg-primary-purple hover:bg-primary-purple/80 text-white p-2 rounded transition-colors"
                  >
                    <Minus size={16} />
                  </button>
                  <input
                    type="number"
                    value={currentLevel}
                    onChange={(e) => setCurrentLevel(Math.max(1, Math.min(99, parseInt(e.target.value) || 1)))}
                    className="flex-1 bg-secondary-dark border border-secondary-dark rounded px-3 py-2 text-center text-white focus:outline-none focus:ring-2 focus:ring-primary-purple font-montserrat"
                    min="1"
                    max="99"
                  />
                  <button
                    onClick={() => adjustCurrentLevel(1)}
                    className="bg-primary-purple hover:bg-primary-purple/80 text-white p-2 rounded transition-colors"
                  >
                    <Plus size={16} />
                  </button>
                </div>
              </div>

              <div className="bg-primary-dark-grey p-4 rounded-lg border border-secondary-dark">
                <label className="block text-sm font-medium mb-2 text-gray-300 font-montserrat">Desired Level</label>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => adjustDesiredLevel(-1)}
                    className="bg-primary-orange hover:bg-primary-orange/80 text-white p-2 rounded transition-colors"
                  >
                    <Minus size={16} />
                  </button>
                  <input
                    type="number"
                    value={desiredLevel}
                    onChange={(e) => setDesiredLevel(Math.max(currentLevel + 1, Math.min(100, parseInt(e.target.value) || currentLevel + 1)))}
                    className="flex-1 bg-secondary-dark border border-secondary-dark rounded px-3 py-2 text-center text-white focus:outline-none focus:ring-2 focus:ring-primary-orange font-montserrat"
                    min={currentLevel + 1}
                    max="100"
                  />
                  <button
                    onClick={() => adjustDesiredLevel(1)}
                    className="bg-primary-orange hover:bg-primary-orange/80 text-white p-2 rounded transition-colors"
                  >
                    <Plus size={16} />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Service Options */}
          <div className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-primary-orange font-montserrat">SERVICE OPTIONS</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-300 font-montserrat">Account Type</label>
                <CustomDropDown
                  options={LolLevelBoostingDropDown}
                  placeholder="Select Account Type"
                  selectedValue={accShared}
                  onSelect={setAccShared}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-300 font-montserrat">Platform</label>
                <CustomDropDown
                  options={platformOptions}
                  placeholder="Select Platform"
                  selectedValue={platform}
                  onSelect={setPlatform}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-300 font-montserrat">Role</label>
                <CustomDropDown
                  options={roleOptions}
                  placeholder="Select Role"
                  selectedValue={role}
                  onSelect={setRole}
                />
              </div>
            </div>
          </div>

          {/* Add-ons */}
          <div className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-primary-orange font-montserrat">ADD-ONS</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center space-x-3 p-3 bg-primary-dark-grey rounded-lg border border-secondary-dark cursor-pointer hover:bg-secondary-dark transition-colors">
                <input
                  type="checkbox"
                  checked={boosters}
                  onChange={(e) => setBoosters(e.target.checked)}
                  className="w-4 h-4 text-primary-purple bg-secondary-dark border-secondary-dark rounded focus:ring-primary-purple"
                />
                <span className="text-white font-montserrat">Play with Boosters (+40%)</span>
              </label>
              <label className="flex items-center space-x-3 p-3 bg-primary-dark-grey rounded-lg border border-secondary-dark cursor-pointer hover:bg-secondary-dark transition-colors">
                <input
                  type="checkbox"
                  checked={priority}
                  onChange={(e) => setPriority(e.target.checked)}
                  className="w-4 h-4 text-primary-purple bg-secondary-dark border-secondary-dark rounded focus:ring-primary-purple"
                />
                <span className="text-white font-montserrat">Priority Order (+20%)</span>
              </label>
              <label className="flex items-center space-x-3 p-3 bg-primary-dark-grey rounded-lg border border-secondary-dark cursor-pointer hover:bg-secondary-dark transition-colors">
                <input
                  type="checkbox"
                  checked={stream}
                  onChange={(e) => setStream(e.target.checked)}
                  className="w-4 h-4 text-primary-purple bg-secondary-dark border-secondary-dark rounded focus:ring-primary-purple"
                />
                <span className="text-white font-montserrat">Stream Game (+10%)</span>
              </label>
            </div>
          </div>

          {/* Discount Code */}
          <DiscountCodeComponent setDiscount={setDiscount} discount={discount} />

          {/* Price Summary */}
          <div className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-primary-orange font-montserrat">PRICE SUMMARY</h2>
            
            {/* Original and Final Price */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center p-3 bg-primary-dark-grey rounded-lg border border-secondary-dark">
                <div className="text-sm text-gray-400 font-montserrat mb-1">Original Price</div>
                <div className={`text-xl font-bold font-montserrat ${discount < 1 ? 'line-through text-gray-400' : 'text-white'}`}>
                  ${originalPrice.toFixed(2)}
                </div>
              </div>
              <div className="text-center p-3 bg-primary-dark-grey rounded-lg border border-secondary-dark">
                <div className="text-sm text-gray-400 font-montserrat mb-1">Final Price</div>
                <div className="text-xl font-bold text-green-400 font-montserrat">
                  ${totalPrice.toFixed(2)}
                </div>
              </div>
            </div>

            {/* Completion Time */}
            <div className="text-center p-3 bg-primary-dark-grey rounded-lg border border-secondary-dark">
              <div className="text-sm text-gray-400 font-montserrat mb-1">Estimated Completion Time</div>
              <div className="text-lg font-bold text-primary-yellow font-montserrat">
                {completionTime.days > 0 ? `${completionTime.days} days ` : ''}{completionTime.hours} hours
              </div>
            </div>
          </div>

          {/* Order Button */}
          <button
            onClick={handleSubmit}
            disabled={loading || desiredLevel <= currentLevel}
            className="w-full bg-gradient-to-r from-primary-yellow to-primary-orange text-black font-bold py-4 px-6 rounded-lg hover:from-primary-yellow/80 hover:to-primary-orange/80 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed font-montserrat text-lg"
          >
            {loading ? 'Creating Order...' : `ORDER NOW - $${totalPrice.toFixed(2)}`}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LevelBoostingComponent;
