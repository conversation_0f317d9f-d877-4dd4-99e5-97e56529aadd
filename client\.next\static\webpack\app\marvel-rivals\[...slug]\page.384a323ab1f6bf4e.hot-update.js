"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/marvel-rivals/[...slug]/page",{

/***/ "(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx":
/*!*******************************************************************!*\
  !*** ./app/marvel-rivals/[...slug]/components/level-boosting.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_discountCode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/discountCode */ \"(app-pages-browser)/./components/discountCode.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-loader-spinner */ \"(app-pages-browser)/./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _constants_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/common */ \"(app-pages-browser)/./constants/common.ts\");\n/* harmony import */ var _constants_service_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/constants/service-types */ \"(app-pages-browser)/./constants/service-types.ts\");\n/* harmony import */ var _database_functions_database__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/database/functions.database */ \"(app-pages-browser)/./database/functions.database.ts\");\n/* harmony import */ var _utils_now_payments__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/now-payments */ \"(app-pages-browser)/./utils/now-payments.ts\");\n/* harmony import */ var _utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/toast-utils */ \"(app-pages-browser)/./utils/toast-utils.ts\");\n/* harmony import */ var _components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom-dropdown */ \"(app-pages-browser)/./components/custom-dropdown.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst MultiRangeSlider = (param)=>{\n    let { min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange } = param;\n    _s();\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    // Level markers for the slider\n    const levelMarkers = [\n        1,\n        10,\n        20,\n        30,\n        40,\n        50,\n        60,\n        70,\n        80,\n        90,\n        100\n    ];\n    const getPercent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        return (value - min) / (max - min) * 100;\n    }, [\n        min,\n        max\n    ]);\n    const getValueFromPercent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((percent)=>{\n        return Math.round(min + percent / 100 * (max - min));\n    }, [\n        min,\n        max\n    ]);\n    const handleMouseDown = (type)=>(event)=>{\n            if (!sliderRef.current) return;\n            setIsDragging(type);\n            const rect = sliderRef.current.getBoundingClientRect();\n            const handlePosition = type === \"current\" ? getPercent(currentLevel) : getPercent(desiredLevel);\n            const clickPosition = (event.clientX - rect.left) / rect.width * 100;\n            setDragOffset(clickPosition - handlePosition);\n            event.preventDefault();\n        };\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((event)=>{\n        if (!isDragging || !sliderRef.current) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = Math.min(100, Math.max(0, (event.clientX - rect.left) / rect.width * 100 - dragOffset));\n        const value = getValueFromPercent(percent);\n        if (isDragging === \"current\") {\n            const newCurrent = Math.min(Math.max(min, value), desiredLevel - 1);\n            onCurrentChange(newCurrent);\n        } else if (isDragging === \"desired\") {\n            const newDesired = Math.max(Math.min(max, value), currentLevel + 1);\n            onDesiredChange(newDesired);\n        }\n    }, [\n        isDragging,\n        min,\n        max,\n        currentLevel,\n        desiredLevel,\n        onCurrentChange,\n        onDesiredChange,\n        dragOffset,\n        getValueFromPercent\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        setIsDragging(null);\n        setDragOffset(0);\n    }, []);\n    const handleTrackClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((event)=>{\n        if (!sliderRef.current || isDragging) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = (event.clientX - rect.left) / rect.width * 100;\n        const value = getValueFromPercent(percent);\n        const currentPercent = getPercent(currentLevel);\n        const desiredPercent = getPercent(desiredLevel);\n        const clickPercent = percent;\n        // Determine which handle is closer to the click\n        const distanceToCurrent = Math.abs(clickPercent - currentPercent);\n        const distanceToDesired = Math.abs(clickPercent - desiredPercent);\n        if (distanceToCurrent < distanceToDesired) {\n            // Move current level handle\n            const newCurrent = Math.min(Math.max(min, value), desiredLevel - 1);\n            onCurrentChange(newCurrent);\n        } else {\n            // Move desired level handle\n            const newDesired = Math.max(Math.min(max, value), currentLevel + 1);\n            onDesiredChange(newDesired);\n        }\n    }, [\n        currentLevel,\n        desiredLevel,\n        min,\n        max,\n        onCurrentChange,\n        onDesiredChange,\n        getPercent,\n        getValueFromPercent,\n        isDragging\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    const currentPercent = getPercent(currentLevel);\n    const desiredPercent = getPercent(desiredLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-8 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-6\",\n                ref: sliderRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-8 bg-secondary-dark rounded-lg border border-secondary-dark cursor-pointer\",\n                    onClick: handleTrackClick,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute h-8 bg-gradient-to-r from-primary-yellow to-primary-orange rounded-lg transition-all duration-200 shadow-inner\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                width: \"\".concat(Math.max(0, desiredPercent - currentPercent), \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined),\n                        levelMarkers.map((level)=>{\n                            const percent = getPercent(level);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute w-0.5 h-8 bg-gray-500 opacity-50\",\n                                style: {\n                                    left: \"\".concat(percent, \"%\")\n                                }\n                            }, level, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, undefined);\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-8 h-8 bg-primary-yellow rounded-full border-3 border-white shadow-lg transition-all duration-200 cursor-grab z-30 flex items-center justify-center \".concat(isDragging === \"current\" ? \"cursor-grabbing scale-110\" : \"hover:scale-105\"),\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"current\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-black rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-primary-yellow text-black px-3 py-1 rounded-lg text-xs font-bold whitespace-nowrap font-montserrat shadow-lg\",\n                                    children: [\n                                        \"Current: \",\n                                        currentLevel\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-8 h-8 bg-primary-orange rounded-full border-3 border-white shadow-lg transition-all duration-200 cursor-grab z-30 flex items-center justify-center \".concat(isDragging === \"desired\" ? \"cursor-grabbing scale-110\" : \"hover:scale-105\"),\n                            style: {\n                                left: \"\".concat(desiredPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"desired\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-white rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-primary-orange text-white px-3 py-1 rounded-lg text-xs font-bold whitespace-nowrap font-montserrat shadow-lg\",\n                                    children: [\n                                        \"Target: \",\n                                        desiredLevel\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-4 bg-red-text\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between text-sm text-gray-400 font-montserrat\",\n                    children: levelMarkers.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold text-white \".concat(level === 100 && \"text-end\", \" \"),\n                                    children: level\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, undefined),\n                                level === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"START\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 31\n                                }, undefined),\n                                level === 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"MAX\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, level, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-primary-dark-grey rounded-lg border border-secondary-dark\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-yellow font-bold text-lg font-montserrat\",\n                                        children: currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"CURRENT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-purple font-bold text-lg font-montserrat\",\n                                        children: desiredLevel - currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"LEVELS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-orange font-bold text-lg font-montserrat\",\n                                        children: desiredLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"TARGET\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-400 font-bold font-montserrat\",\n                            children: [\n                                currentLevel,\n                                \" → \",\n                                desiredLevel,\n                                \" (\",\n                                desiredLevel - currentLevel,\n                                \" levels)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MultiRangeSlider, \"L/w+XM8UMMuRGYfWugmZVIgU860=\");\n_c = MultiRangeSlider;\nconst LevelBoostingComponent = (param)=>{\n    let { priceControl } = param;\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [discount, setDiscount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    // Level states\n    const [currentLevel, setCurrentLevel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(21);\n    const [desiredLevel, setDesiredLevel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(64);\n    // Mock data\n    const LolLevelBoostingDropDown = [\n        {\n            id: \"acc1\",\n            label: \"ACC SHARED\",\n            title: \"ACC SHARED\",\n            value: 0,\n            action: \"None\"\n        },\n        {\n            id: \"acc2\",\n            label: \"PLAY W BOOSTERS (100%)\",\n            title: \"PLAY W BOOSTERS (100%)\",\n            value: 100,\n            action: \"Increase\"\n        },\n        {\n            id: \"acc3\",\n            label: \"PLAY W BOOSTERS (200%)\",\n            title: \"PLAY W BOOSTERS (200%)\",\n            value: 200,\n            action: \"Increase\"\n        }\n    ];\n    const platformOptions = [\n        {\n            id: \"pc\",\n            label: \"PC\",\n            title: \"PC\"\n        },\n        {\n            id: \"console\",\n            label: \"CONSOLE\",\n            title: \"CONSOLE\"\n        }\n    ];\n    const roleOptions = [\n        {\n            id: \"any\",\n            label: \"ANY ROLE\",\n            title: \"ANY ROLE\"\n        },\n        {\n            id: \"tank\",\n            label: \"TANK\",\n            title: \"TANK\"\n        },\n        {\n            id: \"damage\",\n            label: \"DAMAGE\",\n            title: \"DAMAGE\"\n        },\n        {\n            id: \"support\",\n            label: \"SUPPORT\",\n            title: \"SUPPORT\"\n        }\n    ];\n    // Selection states\n    const [accShared, setAccShared] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [platform, setPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Price states\n    const [basePrice, setBasePrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [accSharedPrice, setAccSharedPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [boostersPrice, setBoostersPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [priorityPrice, setPriorityPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [streamPrice, setStreamPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [completionTime, setCompletionTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        days: 0,\n        hours: 0\n    });\n    const [total, setTotal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        price: \"5.21\"\n    });\n    const [priceCt, setPriceCt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    // Add-on states\n    const [boosters, setBoosters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [offline, setOffline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [priority, setPriority] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Level adjustment functions\n    const adjustCurrentLevel = (increment)=>{\n        setCurrentLevel((prev)=>{\n            const newLevel = Math.max(1, Math.min(99, prev + increment));\n            if (newLevel >= desiredLevel) {\n                setDesiredLevel(Math.min(100, newLevel + 1));\n            }\n            return newLevel;\n        });\n    };\n    const adjustDesiredLevel = (increment)=>{\n        setDesiredLevel((prev)=>{\n            const newLevel = Math.max(currentLevel + 1, Math.min(100, prev + increment));\n            return newLevel;\n        });\n    };\n    // Calculate price based on level difference\n    const calculateBasePrice = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return 5.21;\n        }\n        const maxPrice = 2681.80;\n        const minPrice = 5.21;\n        const priceRange = maxPrice - minPrice;\n        const calculatedPrice = minPrice + (levelDifference - 1) / 98 * priceRange;\n        return Math.max(minPrice, calculatedPrice);\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Calculate completion time\n    const calculateCompletionTime = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return {\n                days: 0,\n                hours: 1\n            };\n        }\n        const maxDays = 73;\n        const maxHours = 5;\n        const totalMaxHours = maxDays * 24 + maxHours;\n        const totalHours = levelDifference / 99 * totalMaxHours;\n        const days = Math.floor(totalHours / 24);\n        const hours = Math.round(totalHours % 24);\n        return {\n            days,\n            hours\n        };\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Handle ACC SHARED selection\n    const handleAccSharedChange = (option)=>{\n        setAccShared(option);\n        if (option.label === \"PLAY W BOOSTERS (100%)\") {\n            setAccSharedPrice(basePrice * 1.0);\n        } else if (option.label === \"PLAY W BOOSTERS (200%)\") {\n            setAccSharedPrice(basePrice * 2.0);\n        } else {\n            setAccSharedPrice(0);\n        }\n    };\n    // Handle addon changes with proper price calculation\n    const handleAddonChange = (isEnabled, percentage, setPriceFunction)=>{\n        if (isEnabled) {\n            setPriceFunction(basePrice * percentage);\n        } else {\n            setPriceFunction(0);\n        }\n    };\n    // Calculate total price\n    const updateTotalPrice = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        const subtotal = basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice;\n        const finalPrice = subtotal * discount;\n        if (priceCt && Object.keys(priceCt).length > 0) {\n            try {\n                const result = (0,_utils_now_payments__WEBPACK_IMPORTED_MODULE_8__.calcTotalPricePrecentage)(priceCt, finalPrice);\n                setTotal(result);\n            } catch (error) {\n                console.error(\"Price calculation error:\", error);\n                setTotal({\n                    price: finalPrice.toFixed(2)\n                });\n            }\n        } else {\n            setTotal({\n                price: finalPrice.toFixed(2)\n            });\n        }\n    }, [\n        basePrice,\n        accSharedPrice,\n        boostersPrice,\n        priorityPrice,\n        streamPrice,\n        discount,\n        priceCt\n    ]);\n    // Handle order creation with proper validation and error handling\n    const handleSubmit = async ()=>{\n        // Validation\n        if (desiredLevel <= currentLevel) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Desired level must be higher than current level!\");\n            return;\n        }\n        if (!accShared) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Please select account sharing option!\");\n            return;\n        }\n        if (!platform) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Please select a platform!\");\n            return;\n        }\n        if (!role) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Please select a role!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const orderId = await (0,_database_functions_database__WEBPACK_IMPORTED_MODULE_7__.createOrder)({\n                game_name: _constants_common__WEBPACK_IMPORTED_MODULE_5__.GameNames.LeagueOfLegends,\n                service_type: _constants_service_types__WEBPACK_IMPORTED_MODULE_6__.MarvelRivalsServiceTypes.LevelBoosting,\n                order_details: JSON.stringify({\n                    currentLevel,\n                    desiredLevel,\n                    levelDifference: desiredLevel - currentLevel,\n                    platform: platform.label,\n                    role: role.label,\n                    accShared: accShared.label,\n                    addons: {\n                        boosters,\n                        offline,\n                        roles,\n                        priority,\n                        stream\n                    }\n                }),\n                play_with_boosters: boosters,\n                appear_offline: offline,\n                champions_roles: roles,\n                priority_order: priority,\n                stream_game: stream,\n                completion_time_hours: completionTime.days * 24 + completionTime.hours,\n                price: parseFloat(total === null || total === void 0 ? void 0 : total.price) || basePrice\n            });\n            if (orderId) {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.successToast)(\"Order created successfully!\");\n                router.push(\"/checkout?order_id=\".concat(orderId));\n            } else {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Failed to create order. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Order creation failed:\", error);\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Failed to create order. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Get price control\n    const getPriceControl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(async ()=>{\n        try {\n            setPriceCt(priceControl || {});\n        } catch (error) {\n            console.error(\"Price control error:\", error);\n            setPriceCt({});\n        }\n    }, [\n        priceControl\n    ]);\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const newPrice = calculateBasePrice();\n        setBasePrice(newPrice);\n        const newTime = calculateCompletionTime();\n        setCompletionTime(newTime);\n    }, [\n        calculateBasePrice,\n        calculateCompletionTime\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        updateTotalPrice();\n    }, [\n        updateTotalPrice\n    ]);\n    // Update prices when base price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (accShared) {\n            handleAccSharedChange(accShared);\n        }\n    }, [\n        basePrice\n    ]);\n    // Handle addon price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        handleAddonChange(boosters, 0.4, setBoostersPrice);\n    }, [\n        boosters,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        handleAddonChange(priority, 0.2, setPriorityPrice);\n    }, [\n        priority,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        handleAddonChange(stream, 0.1, setStreamPrice);\n    }, [\n        stream,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getPriceControl();\n    }, [\n        getPriceControl\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"col-span-12 \",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-12 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-9 bg-primary-dark rounded-md px-6 py-8 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center text-xl font-bold mr-4 text-white\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white font-montserrat\",\n                                                children: \"SELECT YOUR CURRENT AND DESIRED LEVEL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-4xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"CURRENT LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-yellow font-montserrat\",\n                                                                    children: currentLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel <= 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 528,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 523,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-yellow text-black px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: currentLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel >= 99,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 538,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"DESIRED LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-orange font-montserrat\",\n                                                                    children: desiredLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel <= currentLevel + 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 553,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-orange text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: desiredLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 555,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel >= 100,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 563,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiRangeSlider, {\n                                                    min: 1,\n                                                    max: 100,\n                                                    currentLevel: currentLevel,\n                                                    desiredLevel: desiredLevel,\n                                                    onCurrentChange: setCurrentLevel,\n                                                    onDesiredChange: setDesiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid items-center grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"ACC SHARED OR PLAY\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"W BOOSTERS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                options: LolLevelBoostingDropDown,\n                                                selectedValue: accShared,\n                                                onSelect: handleAccSharedChange,\n                                                placeholder: \"ACC SHARED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT ROLE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                options: roleOptions,\n                                                selectedValue: role,\n                                                onSelect: setRole,\n                                                placeholder: \"ANY ROLE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT PLATFORM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                options: platformOptions,\n                                                selectedValue: platform,\n                                                onSelect: setPlatform,\n                                                placeholder: \"PC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-3 bg-primary-dark rounded-md p-2 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-secondary-dark p-4 rounded-lg mb-6 flex items-center justify-center border border-secondary-dark\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400 mb-2 font-montserrat\",\n                                            children: \"BOOST FROM LEVEL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold mb-3 font-montserrat\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-yellow\",\n                                                    children: currentLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 mx-2\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-orange\",\n                                                    children: desiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-primary-yellow to-primary-orange text-white px-4 py-2 rounded-full text-sm font-bold font-montserrat\",\n                                            children: [\n                                                \"+\",\n                                                desiredLevel - currentLevel,\n                                                \" LEVELS\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Play with Boosters (+40%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: boosters,\n                                                        onChange: ()=>setBoosters(!boosters),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Appear Offline (Free)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: offline,\n                                                        onChange: ()=>setOffline(!offline),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Champions/Roles (Free)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: roles,\n                                                        onChange: ()=>setRoles(!roles),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Priority Order (+20%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: priority,\n                                                        onChange: ()=>setPriority(!priority),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Stream Games (+10%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: stream,\n                                                        onChange: ()=>setStream(!stream),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_discountCode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                setDiscount: setDiscount,\n                                discount: discount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 720,\n                                columnNumber: 13\n                            }, undefined),\n                            discount < 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-medium text-gray-400\",\n                                                children: \"Original Price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-medium text-gray-400 line-through\",\n                                                children: [\n                                                    \"$\",\n                                                    (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-sm border border-green-500 p-3 flex justify-between items-center bg-[#1C1E2B]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-semibold text-green-400\",\n                                                children: [\n                                                    \"Total Amount (\",\n                                                    Math.round((1 - discount) * 100),\n                                                    \"% OFF)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-semibold text-green-400\",\n                                                children: [\n                                                    \"$\",\n                                                    (total === null || total === void 0 ? void 0 : total.price) || ((basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice) * discount).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 724,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-montserrat font-semibold\",\n                                        children: \"Total Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-montserrat font-semibold\",\n                                        children: [\n                                            \"$\",\n                                            (total === null || total === void 0 ? void 0 : total.price) || (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 15\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                disabled: true,\n                                type: \"button\",\n                                className: \"flex justify-center items-center text-white bg-secondary-600 font-medium w-full rounded-lg text-sm me-2 mb-2 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_3__.Bars, {\n                                    height: \"40\",\n                                    color: \"white\",\n                                    ariaLabel: \"bars-loading\",\n                                    wrapperStyle: {},\n                                    wrapperClass: \"\",\n                                    visible: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 752,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleSubmit,\n                                className: \"text-white bg-primary-purple hover:bg-primary-shaded-purple focus:ring-4 w-full focus:ring-blue-300 font-medium rounded-lg text-sm px-7 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\",\n                                children: \"Level Up Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 767,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-xs text-gray-400 mt-4 font-montserrat\",\n                                children: [\n                                    \"Completion Time: \",\n                                    completionTime.days,\n                                    \" Days \",\n                                    completionTime.hours,\n                                    \" Hours\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 777,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n            lineNumber: 501,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 500,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(LevelBoostingComponent, \"6QjUyUuzW16cIsH+qP6AcXp4LIs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c1 = LevelBoostingComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LevelBoostingComponent);\nvar _c, _c1;\n$RefreshReg$(_c, \"MultiRangeSlider\");\n$RefreshReg$(_c1, \"LevelBoostingComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx\n"));

/***/ })

});