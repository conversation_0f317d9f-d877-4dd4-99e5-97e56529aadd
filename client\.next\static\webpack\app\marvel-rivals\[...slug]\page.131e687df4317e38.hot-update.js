"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/marvel-rivals/[...slug]/page",{

/***/ "(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx":
/*!*******************************************************************!*\
  !*** ./app/marvel-rivals/[...slug]/components/level-boosting.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_discountCode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/discountCode */ \"(app-pages-browser)/./components/discountCode.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-loader-spinner */ \"(app-pages-browser)/./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _constants_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/common */ \"(app-pages-browser)/./constants/common.ts\");\n/* harmony import */ var _constants_service_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/constants/service-types */ \"(app-pages-browser)/./constants/service-types.ts\");\n/* harmony import */ var _database_functions_database__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/database/functions.database */ \"(app-pages-browser)/./database/functions.database.ts\");\n/* harmony import */ var _utils_now_payments__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/now-payments */ \"(app-pages-browser)/./utils/now-payments.ts\");\n/* harmony import */ var _utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/toast-utils */ \"(app-pages-browser)/./utils/toast-utils.ts\");\n/* harmony import */ var _components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom-dropdown */ \"(app-pages-browser)/./components/custom-dropdown.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst MultiRangeSlider = (param)=>{\n    let { min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange } = param;\n    _s();\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const getPercent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>Math.round((value - min) / (max - min) * 100), [\n        min,\n        max\n    ]);\n    const handleMouseDown = (type)=>(event)=>{\n            setIsDragging(type);\n            event.preventDefault();\n        };\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((event)=>{\n        if (!isDragging || !sliderRef.current) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = Math.min(100, Math.max(0, (event.clientX - rect.left) / rect.width * 100));\n        const value = Math.round(min + percent / 100 * (max - min));\n        if (isDragging === \"current\") {\n            const newCurrent = Math.min(value, desiredLevel - 1);\n            onCurrentChange(Math.max(min, newCurrent));\n        } else if (isDragging === \"desired\") {\n            const newDesired = Math.max(value, currentLevel + 1);\n            onDesiredChange(Math.min(max, newDesired));\n        }\n    }, [\n        isDragging,\n        min,\n        max,\n        currentLevel,\n        desiredLevel,\n        onCurrentChange,\n        onDesiredChange\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        setIsDragging(null);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    const currentPercent = getPercent(currentLevel);\n    const desiredPercent = getPercent(desiredLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-8 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-4\",\n                ref: sliderRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-6 bg-secondary-dark rounded-lg border border-secondary-dark\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute h-6 bg-gradient-to-r from-primary-yellow to-primary-orange rounded-lg transition-all duration-300\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                width: \"\".concat(desiredPercent - currentPercent, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-6 h-6 bg-primary-yellow rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-primary-yellow/80 z-20\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"current\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-primary-yellow text-black px-2 py-1 rounded text-xs font-bold whitespace-nowrap font-montserrat\",\n                                children: [\n                                    \"Current: \",\n                                    currentLevel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-6 h-6 bg-primary-orange rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-primary-orange/80 z-20\",\n                            style: {\n                                left: \"\".concat(desiredPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"desired\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-primary-orange text-white px-2 py-1 rounded text-xs font-bold whitespace-nowrap font-montserrat\",\n                                children: [\n                                    \"Target: \",\n                                    desiredLevel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between text-sm text-gray-400 mb-4 font-montserrat\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold\",\n                            children: \"1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold\",\n                            children: \"20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold\",\n                            children: \"30\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold\",\n                            children: \"40\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold\",\n                            children: \"50\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold\",\n                            children: \"60\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold\",\n                            children: \"70\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"100\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"MAX\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MultiRangeSlider, \"IBff4LxYZyFuRUNRi8ivWheMclo=\");\n_c = MultiRangeSlider;\nconst LevelBoostingComponent = (param)=>{\n    let { priceControl } = param;\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [discount, setDiscount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    // Level states\n    const [currentLevel, setCurrentLevel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(21);\n    const [desiredLevel, setDesiredLevel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(64);\n    // Mock data\n    const LolLevelBoostingDropDown = [\n        {\n            id: \"acc1\",\n            label: \"ACC SHARED\",\n            title: \"ACC SHARED\",\n            value: 0,\n            action: \"None\"\n        },\n        {\n            id: \"acc2\",\n            label: \"PLAY W BOOSTERS (100%)\",\n            title: \"PLAY W BOOSTERS (100%)\",\n            value: 100,\n            action: \"Increase\"\n        },\n        {\n            id: \"acc3\",\n            label: \"PLAY W BOOSTERS (200%)\",\n            title: \"PLAY W BOOSTERS (200%)\",\n            value: 200,\n            action: \"Increase\"\n        }\n    ];\n    const platformOptions = [\n        {\n            id: \"pc\",\n            label: \"PC\",\n            title: \"PC\"\n        },\n        {\n            id: \"console\",\n            label: \"CONSOLE\",\n            title: \"CONSOLE\"\n        }\n    ];\n    const roleOptions = [\n        {\n            id: \"any\",\n            label: \"ANY ROLE\",\n            title: \"ANY ROLE\"\n        },\n        {\n            id: \"tank\",\n            label: \"TANK\",\n            title: \"TANK\"\n        },\n        {\n            id: \"damage\",\n            label: \"DAMAGE\",\n            title: \"DAMAGE\"\n        },\n        {\n            id: \"support\",\n            label: \"SUPPORT\",\n            title: \"SUPPORT\"\n        }\n    ];\n    // Selection states\n    const [accShared, setAccShared] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [platform, setPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Price states\n    const [basePrice, setBasePrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [accSharedPrice, setAccSharedPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [boostersPrice, setBoostersPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [priorityPrice, setPriorityPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [streamPrice, setStreamPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [completionTime, setCompletionTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        days: 0,\n        hours: 0\n    });\n    const [total, setTotal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        price: \"5.21\"\n    });\n    const [priceCt, setPriceCt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    // Add-on states\n    const [boosters, setBoosters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [offline, setOffline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [priority, setPriority] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Level adjustment functions\n    const adjustCurrentLevel = (increment)=>{\n        setCurrentLevel((prev)=>{\n            const newLevel = Math.max(1, Math.min(99, prev + increment));\n            if (newLevel >= desiredLevel) {\n                setDesiredLevel(Math.min(100, newLevel + 1));\n            }\n            return newLevel;\n        });\n    };\n    const adjustDesiredLevel = (increment)=>{\n        setDesiredLevel((prev)=>{\n            const newLevel = Math.max(currentLevel + 1, Math.min(100, prev + increment));\n            return newLevel;\n        });\n    };\n    // Calculate price based on level difference\n    const calculateBasePrice = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return 5.21;\n        }\n        const maxPrice = 2681.80;\n        const minPrice = 5.21;\n        const priceRange = maxPrice - minPrice;\n        const calculatedPrice = minPrice + (levelDifference - 1) / 98 * priceRange;\n        return Math.max(minPrice, calculatedPrice);\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Calculate completion time\n    const calculateCompletionTime = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return {\n                days: 0,\n                hours: 1\n            };\n        }\n        const maxDays = 73;\n        const maxHours = 5;\n        const totalMaxHours = maxDays * 24 + maxHours;\n        const totalHours = levelDifference / 99 * totalMaxHours;\n        const days = Math.floor(totalHours / 24);\n        const hours = Math.round(totalHours % 24);\n        return {\n            days,\n            hours\n        };\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Handle ACC SHARED selection\n    const handleAccSharedChange = (option)=>{\n        setAccShared(option);\n        if (option.label === \"PLAY W BOOSTERS (100%)\") {\n            setAccSharedPrice(basePrice * 1.0);\n        } else if (option.label === \"PLAY W BOOSTERS (200%)\") {\n            setAccSharedPrice(basePrice * 2.0);\n        } else {\n            setAccSharedPrice(0);\n        }\n    };\n    // Handle addon changes with proper price calculation\n    const handleAddonChange = (isEnabled, percentage, setPriceFunction)=>{\n        if (isEnabled) {\n            setPriceFunction(basePrice * percentage);\n        } else {\n            setPriceFunction(0);\n        }\n    };\n    // Calculate total price\n    const updateTotalPrice = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        const subtotal = basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice;\n        const finalPrice = subtotal * discount;\n        if (priceCt && Object.keys(priceCt).length > 0) {\n            try {\n                const result = (0,_utils_now_payments__WEBPACK_IMPORTED_MODULE_8__.calcTotalPricePrecentage)(priceCt, finalPrice);\n                setTotal(result);\n            } catch (error) {\n                console.error(\"Price calculation error:\", error);\n                setTotal({\n                    price: finalPrice.toFixed(2)\n                });\n            }\n        } else {\n            setTotal({\n                price: finalPrice.toFixed(2)\n            });\n        }\n    }, [\n        basePrice,\n        accSharedPrice,\n        boostersPrice,\n        priorityPrice,\n        streamPrice,\n        discount,\n        priceCt\n    ]);\n    // Handle order creation with proper validation and error handling\n    const handleSubmit = async ()=>{\n        // Validation\n        if (desiredLevel <= currentLevel) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Desired level must be higher than current level!\");\n            return;\n        }\n        if (!accShared) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Please select account sharing option!\");\n            return;\n        }\n        if (!platform) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Please select a platform!\");\n            return;\n        }\n        if (!role) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Please select a role!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const orderId = await (0,_database_functions_database__WEBPACK_IMPORTED_MODULE_7__.createOrder)({\n                game_name: _constants_common__WEBPACK_IMPORTED_MODULE_5__.GameNames.LeagueOfLegends,\n                service_type: _constants_service_types__WEBPACK_IMPORTED_MODULE_6__.MarvelRivalsServiceTypes.LevelBoosting,\n                order_details: JSON.stringify({\n                    currentLevel,\n                    desiredLevel,\n                    levelDifference: desiredLevel - currentLevel,\n                    platform: platform.label,\n                    role: role.label,\n                    accShared: accShared.label,\n                    addons: {\n                        boosters,\n                        offline,\n                        roles,\n                        priority,\n                        stream\n                    }\n                }),\n                play_with_boosters: boosters,\n                appear_offline: offline,\n                champions_roles: roles,\n                priority_order: priority,\n                stream_game: stream,\n                completion_time_hours: completionTime.days * 24 + completionTime.hours,\n                price: parseFloat(total === null || total === void 0 ? void 0 : total.price) || basePrice\n            });\n            if (orderId) {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.successToast)(\"Order created successfully!\");\n                router.push(\"/checkout?order_id=\".concat(orderId));\n            } else {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Failed to create order. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Order creation failed:\", error);\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Failed to create order. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Get price control\n    const getPriceControl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(async ()=>{\n        try {\n            setPriceCt(priceControl || {});\n        } catch (error) {\n            console.error(\"Price control error:\", error);\n            setPriceCt({});\n        }\n    }, [\n        priceControl\n    ]);\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const newPrice = calculateBasePrice();\n        setBasePrice(newPrice);\n        const newTime = calculateCompletionTime();\n        setCompletionTime(newTime);\n    }, [\n        calculateBasePrice,\n        calculateCompletionTime\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        updateTotalPrice();\n    }, [\n        updateTotalPrice\n    ]);\n    // Update prices when base price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (accShared) {\n            handleAccSharedChange(accShared);\n        }\n    }, [\n        basePrice\n    ]);\n    // Handle addon price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        handleAddonChange(boosters, 0.4, setBoostersPrice);\n    }, [\n        boosters,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        handleAddonChange(priority, 0.2, setPriorityPrice);\n    }, [\n        priority,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        handleAddonChange(stream, 0.1, setStreamPrice);\n    }, [\n        stream,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getPriceControl();\n    }, [\n        getPriceControl\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"col-span-12 \",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-12 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-9 bg-primary-dark rounded-md px-6 py-8 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center text-xl font-bold mr-4 text-white\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white font-montserrat\",\n                                                children: \"SELECT YOUR CURRENT AND DESIRED LEVEL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-4xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"CURRENT LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-yellow font-montserrat\",\n                                                                    children: currentLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel <= 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 457,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 452,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-yellow text-black px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: currentLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel >= 99,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 467,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 462,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"DESIRED LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-orange font-montserrat\",\n                                                                    children: desiredLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel <= currentLevel + 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-orange text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: desiredLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 484,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel >= 100,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 492,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 487,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiRangeSlider, {\n                                                    min: 1,\n                                                    max: 100,\n                                                    currentLevel: currentLevel,\n                                                    desiredLevel: desiredLevel,\n                                                    onCurrentChange: setCurrentLevel,\n                                                    onDesiredChange: setDesiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid items-center grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"ACC SHARED OR PLAY\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"W BOOSTERS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                options: LolLevelBoostingDropDown,\n                                                selectedValue: accShared,\n                                                onSelect: handleAccSharedChange,\n                                                placeholder: \"ACC SHARED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT ROLE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                options: roleOptions,\n                                                selectedValue: role,\n                                                onSelect: setRole,\n                                                placeholder: \"ANY ROLE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT PLATFORM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                options: platformOptions,\n                                                selectedValue: platform,\n                                                onSelect: setPlatform,\n                                                placeholder: \"PC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-3 bg-primary-dark rounded-md p-2 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-secondary-dark p-4 rounded-lg mb-6 flex items-center justify-center border border-secondary-dark\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400 mb-2 font-montserrat\",\n                                            children: \"BOOST FROM LEVEL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold mb-3 font-montserrat\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-yellow\",\n                                                    children: currentLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 mx-2\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-orange\",\n                                                    children: desiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-primary-yellow to-primary-orange text-white px-4 py-2 rounded-full text-sm font-bold font-montserrat\",\n                                            children: [\n                                                \"+\",\n                                                desiredLevel - currentLevel,\n                                                \" LEVELS\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 566,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Play with Boosters (+40%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: boosters,\n                                                        onChange: ()=>setBoosters(!boosters),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Appear Offline (Free)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: offline,\n                                                        onChange: ()=>setOffline(!offline),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Champions/Roles (Free)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: roles,\n                                                        onChange: ()=>setRoles(!roles),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Priority Order (+20%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: priority,\n                                                        onChange: ()=>setPriority(!priority),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Stream Games (+10%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: stream,\n                                                        onChange: ()=>setStream(!stream),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_discountCode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                setDiscount: setDiscount,\n                                discount: discount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 13\n                            }, undefined),\n                            discount < 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-medium text-gray-400\",\n                                                children: \"Original Price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-medium text-gray-400 line-through\",\n                                                children: [\n                                                    \"$\",\n                                                    (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-sm border border-green-500 p-3 flex justify-between items-center bg-[#1C1E2B]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-semibold text-green-400\",\n                                                children: [\n                                                    \"Total Amount (\",\n                                                    Math.round((1 - discount) * 100),\n                                                    \"% OFF)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-semibold text-green-400\",\n                                                children: [\n                                                    \"$\",\n                                                    (total === null || total === void 0 ? void 0 : total.price) || ((basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice) * discount).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-montserrat font-semibold\",\n                                        children: \"Total Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-montserrat font-semibold\",\n                                        children: [\n                                            \"$\",\n                                            (total === null || total === void 0 ? void 0 : total.price) || (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 671,\n                                columnNumber: 15\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                disabled: true,\n                                type: \"button\",\n                                className: \"flex justify-center items-center text-white bg-secondary-600 font-medium w-full rounded-lg text-sm me-2 mb-2 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_3__.Bars, {\n                                    height: \"40\",\n                                    color: \"white\",\n                                    ariaLabel: \"bars-loading\",\n                                    wrapperStyle: {},\n                                    wrapperClass: \"\",\n                                    visible: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 686,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleSubmit,\n                                className: \"text-white bg-primary-purple hover:bg-primary-shaded-purple focus:ring-4 w-full focus:ring-blue-300 font-medium rounded-lg text-sm px-7 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\",\n                                children: \"Level Up Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-xs text-gray-400 mt-4 font-montserrat\",\n                                children: [\n                                    \"Completion Time: \",\n                                    completionTime.days,\n                                    \" Days \",\n                                    completionTime.hours,\n                                    \" Hours\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 706,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 431,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n            lineNumber: 430,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 429,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(LevelBoostingComponent, \"6QjUyUuzW16cIsH+qP6AcXp4LIs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c1 = LevelBoostingComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LevelBoostingComponent);\nvar _c, _c1;\n$RefreshReg$(_c, \"MultiRangeSlider\");\n$RefreshReg$(_c1, \"LevelBoostingComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx\n"));

/***/ })

});