"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/marvel-rivals/[...slug]/page",{

/***/ "(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx":
/*!*******************************************************************!*\
  !*** ./app/marvel-rivals/[...slug]/components/level-boosting.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_discountCode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/discountCode */ \"(app-pages-browser)/./components/discountCode.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-loader-spinner */ \"(app-pages-browser)/./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _constants_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/common */ \"(app-pages-browser)/./constants/common.ts\");\n/* harmony import */ var _constants_service_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/constants/service-types */ \"(app-pages-browser)/./constants/service-types.ts\");\n/* harmony import */ var _database_functions_database__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/database/functions.database */ \"(app-pages-browser)/./database/functions.database.ts\");\n/* harmony import */ var _utils_now_payments__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/now-payments */ \"(app-pages-browser)/./utils/now-payments.ts\");\n/* harmony import */ var _utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/toast-utils */ \"(app-pages-browser)/./utils/toast-utils.ts\");\n/* harmony import */ var _components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/custom-dropdown */ \"(app-pages-browser)/./components/custom-dropdown.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst MultiRangeSlider = (param)=>{\n    let { min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange } = param;\n    _s();\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const getPercent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>Math.round((value - min) / (max - min) * 100), [\n        min,\n        max\n    ]);\n    const handleMouseDown = (type)=>(event)=>{\n            setIsDragging(type);\n            event.preventDefault();\n        };\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((event)=>{\n        if (!isDragging || !sliderRef.current) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = Math.min(100, Math.max(0, (event.clientX - rect.left) / rect.width * 100));\n        const value = Math.round(min + percent / 100 * (max - min));\n        if (isDragging === \"current\") {\n            const newCurrent = Math.min(value, desiredLevel - 1);\n            onCurrentChange(Math.max(min, newCurrent));\n        } else if (isDragging === \"desired\") {\n            const newDesired = Math.max(value, currentLevel + 1);\n            onDesiredChange(Math.min(max, newDesired));\n        }\n    }, [\n        isDragging,\n        min,\n        max,\n        currentLevel,\n        desiredLevel,\n        onCurrentChange,\n        onDesiredChange\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        setIsDragging(null);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    const currentPercent = getPercent(currentLevel);\n    const desiredPercent = getPercent(desiredLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-8 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-4\",\n                ref: sliderRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-6 bg-secondary-dark rounded-lg border border-secondary-dark\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute h-6 bg-gradient-to-r from-primary-yellow to-primary-orange rounded-lg transition-all duration-300\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                width: \"\".concat(desiredPercent - currentPercent, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-6 h-6 bg-primary-yellow rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-primary-yellow/80 z-20\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"current\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-primary-yellow text-black px-2 py-1 rounded text-xs font-bold whitespace-nowrap font-montserrat\",\n                                children: [\n                                    \"Current: \",\n                                    currentLevel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-6 h-6 bg-primary-orange rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-primary-orange/80 z-20\",\n                            style: {\n                                left: \"\".concat(desiredPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"desired\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-primary-orange text-white px-2 py-1 rounded text-xs font-bold whitespace-nowrap font-montserrat\",\n                                children: [\n                                    \"Target: \",\n                                    desiredLevel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between text-sm text-gray-400 mb-4 font-montserrat\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold\",\n                            children: \"1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold\",\n                            children: \"25\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"MID\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"75\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"HIGH\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"100\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"MAX\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MultiRangeSlider, \"IBff4LxYZyFuRUNRi8ivWheMclo=\");\n_c = MultiRangeSlider;\nconst LevelBoostingComponent = (param)=>{\n    let { priceControl } = param;\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [discount, setDiscount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    // Level states\n    const [currentLevel, setCurrentLevel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(21);\n    const [desiredLevel, setDesiredLevel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(64);\n    // Mock data\n    const LolLevelBoostingDropDown = [\n        {\n            id: \"acc1\",\n            label: \"ACC SHARED\",\n            title: \"ACC SHARED\",\n            value: 0,\n            action: \"None\"\n        },\n        {\n            id: \"acc2\",\n            label: \"PLAY W BOOSTERS (100%)\",\n            title: \"PLAY W BOOSTERS (100%)\",\n            value: 100,\n            action: \"Increase\"\n        },\n        {\n            id: \"acc3\",\n            label: \"PLAY W BOOSTERS (200%)\",\n            title: \"PLAY W BOOSTERS (200%)\",\n            value: 200,\n            action: \"Increase\"\n        }\n    ];\n    const platformOptions = [\n        {\n            id: \"pc\",\n            label: \"PC\",\n            title: \"PC\"\n        },\n        {\n            id: \"console\",\n            label: \"CONSOLE\",\n            title: \"CONSOLE\"\n        }\n    ];\n    const roleOptions = [\n        {\n            id: \"any\",\n            label: \"ANY ROLE\",\n            title: \"ANY ROLE\"\n        },\n        {\n            id: \"tank\",\n            label: \"TANK\",\n            title: \"TANK\"\n        },\n        {\n            id: \"damage\",\n            label: \"DAMAGE\",\n            title: \"DAMAGE\"\n        },\n        {\n            id: \"support\",\n            label: \"SUPPORT\",\n            title: \"SUPPORT\"\n        }\n    ];\n    // Selection states\n    const [accShared, setAccShared] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [platform, setPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Price states\n    const [basePrice, setBasePrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [accSharedPrice, setAccSharedPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [boostersPrice, setBoostersPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [priorityPrice, setPriorityPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [streamPrice, setStreamPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [completionTime, setCompletionTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        days: 0,\n        hours: 0\n    });\n    const [total, setTotal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        price: \"5.21\"\n    });\n    const [priceCt, setPriceCt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    // Add-on states\n    const [boosters, setBoosters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [offline, setOffline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [priority, setPriority] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Level adjustment functions\n    const adjustCurrentLevel = (increment)=>{\n        setCurrentLevel((prev)=>{\n            const newLevel = Math.max(1, Math.min(99, prev + increment));\n            if (newLevel >= desiredLevel) {\n                setDesiredLevel(Math.min(100, newLevel + 1));\n            }\n            return newLevel;\n        });\n    };\n    const adjustDesiredLevel = (increment)=>{\n        setDesiredLevel((prev)=>{\n            const newLevel = Math.max(currentLevel + 1, Math.min(100, prev + increment));\n            return newLevel;\n        });\n    };\n    // Calculate price based on level difference\n    const calculateBasePrice = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return 5.21;\n        }\n        const maxPrice = 2681.80;\n        const minPrice = 5.21;\n        const priceRange = maxPrice - minPrice;\n        const calculatedPrice = minPrice + (levelDifference - 1) / 98 * priceRange;\n        return Math.max(minPrice, calculatedPrice);\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Calculate completion time\n    const calculateCompletionTime = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return {\n                days: 0,\n                hours: 1\n            };\n        }\n        const maxDays = 73;\n        const maxHours = 5;\n        const totalMaxHours = maxDays * 24 + maxHours;\n        const totalHours = levelDifference / 99 * totalMaxHours;\n        const days = Math.floor(totalHours / 24);\n        const hours = Math.round(totalHours % 24);\n        return {\n            days,\n            hours\n        };\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Handle ACC SHARED selection\n    const handleAccSharedChange = (option)=>{\n        setAccShared(option);\n        if (option.label === \"PLAY W BOOSTERS (100%)\") {\n            setAccSharedPrice(basePrice * 1.0);\n        } else if (option.label === \"PLAY W BOOSTERS (200%)\") {\n            setAccSharedPrice(basePrice * 2.0);\n        } else {\n            setAccSharedPrice(0);\n        }\n    };\n    // Handle addon changes with proper price calculation\n    const handleAddonChange = (isEnabled, percentage, setPriceFunction)=>{\n        if (isEnabled) {\n            setPriceFunction(basePrice * percentage);\n        } else {\n            setPriceFunction(0);\n        }\n    };\n    // Calculate total price\n    const updateTotalPrice = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        const subtotal = basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice;\n        const finalPrice = subtotal * discount;\n        if (priceCt && Object.keys(priceCt).length > 0) {\n            try {\n                const result = (0,_utils_now_payments__WEBPACK_IMPORTED_MODULE_8__.calcTotalPricePrecentage)(priceCt, finalPrice);\n                setTotal(result);\n            } catch (error) {\n                console.error(\"Price calculation error:\", error);\n                setTotal({\n                    price: finalPrice.toFixed(2)\n                });\n            }\n        } else {\n            setTotal({\n                price: finalPrice.toFixed(2)\n            });\n        }\n    }, [\n        basePrice,\n        accSharedPrice,\n        boostersPrice,\n        priorityPrice,\n        streamPrice,\n        discount,\n        priceCt\n    ]);\n    // Handle order creation with proper validation and error handling\n    const handleSubmit = async ()=>{\n        // Validation\n        if (desiredLevel <= currentLevel) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Desired level must be higher than current level!\");\n            return;\n        }\n        if (!accShared) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Please select account sharing option!\");\n            return;\n        }\n        if (!platform) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Please select a platform!\");\n            return;\n        }\n        if (!role) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Please select a role!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const orderId = await (0,_database_functions_database__WEBPACK_IMPORTED_MODULE_7__.createOrder)({\n                game_name: _constants_common__WEBPACK_IMPORTED_MODULE_5__.GameNames.LeagueOfLegends,\n                service_type: _constants_service_types__WEBPACK_IMPORTED_MODULE_6__.MarvelRivalsServiceTypes.LevelBoosting,\n                order_details: JSON.stringify({\n                    currentLevel,\n                    desiredLevel,\n                    levelDifference: desiredLevel - currentLevel,\n                    platform: platform.label,\n                    role: role.label,\n                    accShared: accShared.label,\n                    addons: {\n                        boosters,\n                        offline,\n                        roles,\n                        priority,\n                        stream\n                    }\n                }),\n                play_with_boosters: boosters,\n                appear_offline: offline,\n                champions_roles: roles,\n                priority_order: priority,\n                stream_game: stream,\n                completion_time_hours: completionTime.days * 24 + completionTime.hours,\n                price: parseFloat(total === null || total === void 0 ? void 0 : total.price) || basePrice\n            });\n            if (orderId) {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.successToast)(\"Order created successfully!\");\n                router.push(\"/checkout?order_id=\".concat(orderId));\n            } else {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Failed to create order. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Order creation failed:\", error);\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_9__.errorToast)(\"Failed to create order. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Get price control\n    const getPriceControl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(async ()=>{\n        try {\n            setPriceCt(priceControl || {});\n        } catch (error) {\n            console.error(\"Price control error:\", error);\n            setPriceCt({});\n        }\n    }, [\n        priceControl\n    ]);\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const newPrice = calculateBasePrice();\n        setBasePrice(newPrice);\n        const newTime = calculateCompletionTime();\n        setCompletionTime(newTime);\n    }, [\n        calculateBasePrice,\n        calculateCompletionTime\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        updateTotalPrice();\n    }, [\n        updateTotalPrice\n    ]);\n    // Update prices when base price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (accShared) {\n            handleAccSharedChange(accShared);\n        }\n    }, [\n        basePrice\n    ]);\n    // Handle addon price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        handleAddonChange(boosters, 0.4, setBoostersPrice);\n    }, [\n        boosters,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        handleAddonChange(priority, 0.2, setPriorityPrice);\n    }, [\n        priority,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        handleAddonChange(stream, 0.1, setStreamPrice);\n    }, [\n        stream,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getPriceControl();\n    }, [\n        getPriceControl\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"col-span-12 \",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-12 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-9 bg-primary-dark rounded-md px-6 py-8 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center text-xl font-bold mr-4 text-white\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white font-montserrat\",\n                                                children: \"SELECT YOUR CURRENT AND DESIRED LEVEL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-4xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"CURRENT LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-yellow font-montserrat\",\n                                                                    children: currentLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel <= 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 447,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 442,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-yellow text-black px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: currentLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 449,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel >= 99,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 457,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 452,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"DESIRED LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-orange font-montserrat\",\n                                                                    children: desiredLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel <= currentLevel + 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 472,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 467,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-orange text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: desiredLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel >= 100,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiRangeSlider, {\n                                                    min: 1,\n                                                    max: 100,\n                                                    currentLevel: currentLevel,\n                                                    desiredLevel: desiredLevel,\n                                                    onCurrentChange: setCurrentLevel,\n                                                    onDesiredChange: setDesiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid items-center grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"ACC SHARED OR PLAY\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"W BOOSTERS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                options: LolLevelBoostingDropDown,\n                                                selectedValue: accShared,\n                                                onSelect: handleAccSharedChange,\n                                                placeholder: \"ACC SHARED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT ROLE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                options: roleOptions,\n                                                selectedValue: role,\n                                                onSelect: setRole,\n                                                placeholder: \"ANY ROLE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT PLATFORM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                options: platformOptions,\n                                                selectedValue: platform,\n                                                onSelect: setPlatform,\n                                                placeholder: \"PC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-3 bg-primary-dark rounded-md p-2 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-secondary-dark p-4 rounded-lg mb-6 flex items-center justify-center border border-secondary-dark\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400 mb-2 font-montserrat\",\n                                            children: \"BOOST FROM LEVEL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold mb-3 font-montserrat\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-yellow\",\n                                                    children: currentLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 mx-2\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-orange\",\n                                                    children: desiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-primary-yellow to-primary-orange text-white px-4 py-2 rounded-full text-sm font-bold font-montserrat\",\n                                            children: [\n                                                \"+\",\n                                                desiredLevel - currentLevel,\n                                                \" LEVELS\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Play with Boosters (+40%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: boosters,\n                                                        onChange: ()=>setBoosters(!boosters),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Appear Offline (Free)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: offline,\n                                                        onChange: ()=>setOffline(!offline),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Champions/Roles (Free)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: roles,\n                                                        onChange: ()=>setRoles(!roles),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Priority Order (+20%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: priority,\n                                                        onChange: ()=>setPriority(!priority),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Stream Games (+10%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: stream,\n                                                        onChange: ()=>setStream(!stream),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_discountCode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                setDiscount: setDiscount,\n                                discount: discount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 13\n                            }, undefined),\n                            discount < 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-medium text-gray-400\",\n                                                children: \"Original Price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-medium text-gray-400 line-through\",\n                                                children: [\n                                                    \"$\",\n                                                    (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-sm border border-green-500 p-3 flex justify-between items-center bg-[#1C1E2B]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-semibold text-green-400\",\n                                                children: [\n                                                    \"Total Amount (\",\n                                                    Math.round((1 - discount) * 100),\n                                                    \"% OFF)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-semibold text-green-400\",\n                                                children: [\n                                                    \"$\",\n                                                    (total === null || total === void 0 ? void 0 : total.price) || ((basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice) * discount).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 643,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-montserrat font-semibold\",\n                                        children: \"Total Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-montserrat font-semibold\",\n                                        children: [\n                                            \"$\",\n                                            (total === null || total === void 0 ? void 0 : total.price) || (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 15\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                disabled: true,\n                                type: \"button\",\n                                className: \"flex justify-center items-center text-white bg-secondary-600 font-medium w-full rounded-lg text-sm me-2 mb-2 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_3__.Bars, {\n                                    height: \"40\",\n                                    color: \"white\",\n                                    ariaLabel: \"bars-loading\",\n                                    wrapperStyle: {},\n                                    wrapperClass: \"\",\n                                    visible: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 671,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleSubmit,\n                                className: \"text-white bg-primary-purple hover:bg-primary-shaded-purple focus:ring-4 w-full focus:ring-blue-300 font-medium rounded-lg text-sm px-7 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\",\n                                children: \"Level Up Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 686,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-xs text-gray-400 mt-4 font-montserrat\",\n                                children: [\n                                    \"Completion Time: \",\n                                    completionTime.days,\n                                    \" Days \",\n                                    completionTime.hours,\n                                    \" Hours\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 554,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 421,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n            lineNumber: 420,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 419,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(LevelBoostingComponent, \"6QjUyUuzW16cIsH+qP6AcXp4LIs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c1 = LevelBoostingComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LevelBoostingComponent);\nvar _c, _c1;\n$RefreshReg$(_c, \"MultiRangeSlider\");\n$RefreshReg$(_c1, \"LevelBoostingComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx\n"));

/***/ })

});