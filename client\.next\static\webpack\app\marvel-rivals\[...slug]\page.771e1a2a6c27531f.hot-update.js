"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/marvel-rivals/[...slug]/page",{

/***/ "(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx":
/*!*******************************************************************!*\
  !*** ./app/marvel-rivals/[...slug]/components/level-boosting.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_custom_dropdown__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/custom-dropdown */ \"(app-pages-browser)/./components/custom-dropdown.tsx\");\n/* harmony import */ var _components_discountCode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/discountCode */ \"(app-pages-browser)/./components/discountCode.tsx\");\n/* harmony import */ var _constants_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constants/common */ \"(app-pages-browser)/./constants/common.ts\");\n/* harmony import */ var _constants_service_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants/service-types */ \"(app-pages-browser)/./constants/service-types.ts\");\n/* harmony import */ var _database_functions_database__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/database/functions.database */ \"(app-pages-browser)/./database/functions.database.ts\");\n/* harmony import */ var _utils_now_payments__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/now-payments */ \"(app-pages-browser)/./utils/now-payments.ts\");\n/* harmony import */ var _utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/toast-utils */ \"(app-pages-browser)/./utils/toast-utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-loader-spinner */ \"(app-pages-browser)/./node_modules/react-loader-spinner/dist/module.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst MultiRangeSlider = (param)=>{\n    let { min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange } = param;\n    _s();\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0);\n    // Level markers for the slider - memoized to prevent re-renders\n    const levelMarkers = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)(()=>[\n            1,\n            10,\n            20,\n            30,\n            40,\n            50,\n            60,\n            70,\n            80,\n            90,\n            100\n        ], []);\n    // Calculate percentage position for any level value\n    const getPercent = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)((value)=>{\n        // Ensure value is within bounds\n        const clampedValue = Math.max(min, Math.min(max, value));\n        return (clampedValue - min) / (max - min) * 100;\n    }, [\n        min,\n        max\n    ]);\n    // Get level value from percentage position\n    const getValueFromPercent = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)((percent)=>{\n        const clampedPercent = Math.max(0, Math.min(100, percent));\n        const rawValue = min + clampedPercent / 100 * (max - min);\n        return Math.round(rawValue);\n    }, [\n        min,\n        max\n    ]);\n    // Find the closest level marker to a given value for snapping\n    const snapToNearestMarker = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)((value)=>{\n        let closest = value;\n        let minDistance = Infinity;\n        levelMarkers.forEach((marker)=>{\n            const distance = Math.abs(value - marker);\n            if (distance < minDistance) {\n                minDistance = distance;\n                closest = marker;\n            }\n        });\n        // Only snap if within 3 levels of a marker\n        return minDistance <= 3 ? closest : value;\n    }, []);\n    const handleMouseDown = (type)=>(event)=>{\n            if (!sliderRef.current) return;\n            setIsDragging(type);\n            event.preventDefault();\n            event.stopPropagation();\n        };\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)((event)=>{\n        if (!isDragging || !sliderRef.current) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = (event.clientX - rect.left) / rect.width * 100;\n        const rawValue = getValueFromPercent(percent);\n        const snappedValue = snapToNearestMarker(rawValue);\n        if (isDragging === \"current\") {\n            const newCurrent = Math.min(Math.max(min, snappedValue), desiredLevel - 1);\n            if (newCurrent !== currentLevel) {\n                onCurrentChange(newCurrent);\n            }\n        } else if (isDragging === \"desired\") {\n            const newDesired = Math.max(Math.min(max, snappedValue), currentLevel + 1);\n            if (newDesired !== desiredLevel) {\n                onDesiredChange(newDesired);\n            }\n        }\n    }, [\n        isDragging,\n        min,\n        max,\n        currentLevel,\n        desiredLevel,\n        onCurrentChange,\n        onDesiredChange,\n        getValueFromPercent,\n        snapToNearestMarker\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(()=>{\n        setIsDragging(null);\n    }, []);\n    const handleTrackClick = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)((event)=>{\n        if (!sliderRef.current || isDragging) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = (event.clientX - rect.left) / rect.width * 100;\n        const value = getValueFromPercent(percent);\n        const currentPercent = getPercent(currentLevel);\n        const desiredPercent = getPercent(desiredLevel);\n        const clickPercent = percent;\n        // Determine which handle is closer to the click\n        const distanceToCurrent = Math.abs(clickPercent - currentPercent);\n        const distanceToDesired = Math.abs(clickPercent - desiredPercent);\n        if (distanceToCurrent < distanceToDesired) {\n            // Move current level handle\n            const newCurrent = Math.min(Math.max(min, value), desiredLevel - 1);\n            onCurrentChange(newCurrent);\n        } else {\n            // Move desired level handle\n            const newDesired = Math.max(Math.min(max, value), currentLevel + 1);\n            onDesiredChange(newDesired);\n        }\n    }, [\n        currentLevel,\n        desiredLevel,\n        min,\n        max,\n        onCurrentChange,\n        onDesiredChange,\n        getPercent,\n        getValueFromPercent,\n        isDragging\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    const currentPercent = getPercent(currentLevel);\n    const desiredPercent = getPercent(desiredLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-8 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-6\",\n                ref: sliderRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-8 bg-secondary-dark rounded-lg border border-secondary-dark cursor-pointer\",\n                    onClick: handleTrackClick,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute h-8 bg-gradient-to-r from-primary-yellow to-primary-orange rounded-lg transition-all duration-200 shadow-inner\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                width: \"\".concat(Math.max(0, desiredPercent - currentPercent), \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined),\n                        levelMarkers.map((level)=>{\n                            const percent = getPercent(level);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute w-0.5 h-8 bg-gray-500 opacity-50\",\n                                style: {\n                                    left: \"\".concat(percent, \"%\")\n                                }\n                            }, level, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, undefined);\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-8 h-8 bg-primary-yellow rounded-full border-3 border-white shadow-lg transition-all duration-200 cursor-grab z-30 flex items-center justify-center \".concat(isDragging === \"current\" ? \"cursor-grabbing scale-110\" : \"hover:scale-105\"),\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"current\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-black rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-primary-yellow text-black px-3 py-1 rounded-lg text-xs font-bold whitespace-nowrap font-montserrat shadow-lg\",\n                                    children: [\n                                        \"Current: \",\n                                        currentLevel\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-8 h-8 bg-primary-orange rounded-full border-3 border-white shadow-lg transition-all duration-200 cursor-grab z-30 flex items-center justify-center \".concat(isDragging === \"desired\" ? \"cursor-grabbing scale-110\" : \"hover:scale-105\"),\n                            style: {\n                                left: \"\".concat(desiredPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"desired\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-white rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-primary-orange text-white px-3 py-1 rounded-lg text-xs font-bold whitespace-nowrap font-montserrat shadow-lg\",\n                                    children: [\n                                        \"Target: \",\n                                        desiredLevel\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-4 bg-red-text\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between text-sm text-gray-400 font-montserrat\",\n                    children: levelMarkers.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold text-white \".concat(level === 1 ? \"text-start\" : level === 100 ? \"text-end\" : \"text-center\", \" \"),\n                                    children: level\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, undefined),\n                                level === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"START\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 31\n                                }, undefined),\n                                level === 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"MAX\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, level, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-primary-dark-grey rounded-lg border border-secondary-dark\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-yellow font-bold text-lg font-montserrat\",\n                                        children: currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"CURRENT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-purple font-bold text-lg font-montserrat\",\n                                        children: desiredLevel - currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"LEVELS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-orange font-bold text-lg font-montserrat\",\n                                        children: desiredLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"TARGET\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-400 font-bold font-montserrat\",\n                            children: [\n                                currentLevel,\n                                \" → \",\n                                desiredLevel,\n                                \" (\",\n                                desiredLevel - currentLevel,\n                                \" levels)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MultiRangeSlider, \"S0cTtbMVXIaMXSa21bb79PNLDaQ=\");\n_c = MultiRangeSlider;\nconst LevelBoostingComponent = (param)=>{\n    let { priceControl } = param;\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [discount, setDiscount] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(1);\n    // Level states\n    const [currentLevel, setCurrentLevel] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(21);\n    const [desiredLevel, setDesiredLevel] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(64);\n    // Mock data\n    const LolLevelBoostingDropDown = [\n        {\n            id: \"acc1\",\n            label: \"ACC SHARED\",\n            title: \"ACC SHARED\",\n            value: 0,\n            action: \"None\"\n        },\n        {\n            id: \"acc2\",\n            label: \"PLAY W BOOSTERS (100%)\",\n            title: \"PLAY W BOOSTERS (100%)\",\n            value: 100,\n            action: \"Increase\"\n        },\n        {\n            id: \"acc3\",\n            label: \"PLAY W BOOSTERS (200%)\",\n            title: \"PLAY W BOOSTERS (200%)\",\n            value: 200,\n            action: \"Increase\"\n        }\n    ];\n    const platformOptions = [\n        {\n            id: \"pc\",\n            label: \"PC\",\n            title: \"PC\"\n        },\n        {\n            id: \"console\",\n            label: \"CONSOLE\",\n            title: \"CONSOLE\"\n        }\n    ];\n    const roleOptions = [\n        {\n            id: \"any\",\n            label: \"ANY ROLE\",\n            title: \"ANY ROLE\"\n        },\n        {\n            id: \"tank\",\n            label: \"TANK\",\n            title: \"TANK\"\n        },\n        {\n            id: \"damage\",\n            label: \"DAMAGE\",\n            title: \"DAMAGE\"\n        },\n        {\n            id: \"support\",\n            label: \"SUPPORT\",\n            title: \"SUPPORT\"\n        }\n    ];\n    // Selection states\n    const [accShared, setAccShared] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [platform, setPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    // Price states\n    const [basePrice, setBasePrice] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0);\n    const [accSharedPrice, setAccSharedPrice] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0);\n    const [boostersPrice, setBoostersPrice] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0);\n    const [priorityPrice, setPriorityPrice] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0);\n    const [streamPrice, setStreamPrice] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0);\n    const [completionTime, setCompletionTime] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        days: 0,\n        hours: 0\n    });\n    const [total, setTotal] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        price: \"5.21\"\n    });\n    const [priceCt, setPriceCt] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({});\n    // Add-on states\n    const [boosters, setBoosters] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [offline, setOffline] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [priority, setPriority] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    // Level adjustment functions\n    const adjustCurrentLevel = (increment)=>{\n        setCurrentLevel((prev)=>{\n            const newLevel = Math.max(1, Math.min(99, prev + increment));\n            if (newLevel >= desiredLevel) {\n                setDesiredLevel(Math.min(100, newLevel + 1));\n            }\n            return newLevel;\n        });\n    };\n    const adjustDesiredLevel = (increment)=>{\n        setDesiredLevel((prev)=>{\n            const newLevel = Math.max(currentLevel + 1, Math.min(100, prev + increment));\n            return newLevel;\n        });\n    };\n    // Calculate price based on level difference\n    const calculateBasePrice = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return 5.21;\n        }\n        const maxPrice = 2681.80;\n        const minPrice = 5.21;\n        const priceRange = maxPrice - minPrice;\n        const calculatedPrice = minPrice + (levelDifference - 1) / 98 * priceRange;\n        return Math.max(minPrice, calculatedPrice);\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Calculate completion time\n    const calculateCompletionTime = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return {\n                days: 0,\n                hours: 1\n            };\n        }\n        const maxDays = 73;\n        const maxHours = 5;\n        const totalMaxHours = maxDays * 24 + maxHours;\n        const totalHours = levelDifference / 99 * totalMaxHours;\n        const days = Math.floor(totalHours / 24);\n        const hours = Math.round(totalHours % 24);\n        return {\n            days,\n            hours\n        };\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Handle ACC SHARED selection\n    const handleAccSharedChange = (option)=>{\n        setAccShared(option);\n        if (option.label === \"PLAY W BOOSTERS (100%)\") {\n            setAccSharedPrice(basePrice * 1.0);\n        } else if (option.label === \"PLAY W BOOSTERS (200%)\") {\n            setAccSharedPrice(basePrice * 2.0);\n        } else {\n            setAccSharedPrice(0);\n        }\n    };\n    // Handle addon changes with proper price calculation\n    const handleAddonChange = (isEnabled, percentage, setPriceFunction)=>{\n        if (isEnabled) {\n            setPriceFunction(basePrice * percentage);\n        } else {\n            setPriceFunction(0);\n        }\n    };\n    // Calculate total price\n    const updateTotalPrice = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(()=>{\n        const subtotal = basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice;\n        const finalPrice = subtotal * discount;\n        if (priceCt && Object.keys(priceCt).length > 0) {\n            try {\n                const result = (0,_utils_now_payments__WEBPACK_IMPORTED_MODULE_6__.calcTotalPricePrecentage)(priceCt, finalPrice);\n                setTotal(result);\n            } catch (error) {\n                console.error(\"Price calculation error:\", error);\n                setTotal({\n                    price: finalPrice.toFixed(2)\n                });\n            }\n        } else {\n            setTotal({\n                price: finalPrice.toFixed(2)\n            });\n        }\n    }, [\n        basePrice,\n        accSharedPrice,\n        boostersPrice,\n        priorityPrice,\n        streamPrice,\n        discount,\n        priceCt\n    ]);\n    // Handle order creation with proper validation and error handling\n    const handleSubmit = async ()=>{\n        // Validation\n        if (desiredLevel <= currentLevel) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.errorToast)(\"Desired level must be higher than current level!\");\n            return;\n        }\n        if (!accShared) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.errorToast)(\"Please select account sharing option!\");\n            return;\n        }\n        if (!platform) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.errorToast)(\"Please select a platform!\");\n            return;\n        }\n        if (!role) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.errorToast)(\"Please select a role!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const orderId = await (0,_database_functions_database__WEBPACK_IMPORTED_MODULE_5__.createOrder)({\n                game_name: _constants_common__WEBPACK_IMPORTED_MODULE_3__.GameNames.LeagueOfLegends,\n                service_type: _constants_service_types__WEBPACK_IMPORTED_MODULE_4__.MarvelRivalsServiceTypes.LevelBoosting,\n                order_details: JSON.stringify({\n                    currentLevel,\n                    desiredLevel,\n                    levelDifference: desiredLevel - currentLevel,\n                    platform: platform.label,\n                    role: role.label,\n                    accShared: accShared.label,\n                    addons: {\n                        boosters,\n                        offline,\n                        roles,\n                        priority,\n                        stream\n                    }\n                }),\n                play_with_boosters: boosters,\n                appear_offline: offline,\n                champions_roles: roles,\n                priority_order: priority,\n                stream_game: stream,\n                completion_time_hours: completionTime.days * 24 + completionTime.hours,\n                price: parseFloat(total === null || total === void 0 ? void 0 : total.price) || basePrice\n            });\n            if (orderId) {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.successToast)(\"Order created successfully!\");\n                router.push(\"/checkout?order_id=\".concat(orderId));\n            } else {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.errorToast)(\"Failed to create order. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Order creation failed:\", error);\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.errorToast)(\"Failed to create order. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Get price control\n    const getPriceControl = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(async ()=>{\n        try {\n            setPriceCt(priceControl || {});\n        } catch (error) {\n            console.error(\"Price control error:\", error);\n            setPriceCt({});\n        }\n    }, [\n        priceControl\n    ]);\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        const newPrice = calculateBasePrice();\n        setBasePrice(newPrice);\n        const newTime = calculateCompletionTime();\n        setCompletionTime(newTime);\n    }, [\n        calculateBasePrice,\n        calculateCompletionTime\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        updateTotalPrice();\n    }, [\n        updateTotalPrice\n    ]);\n    // Update prices when base price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (accShared) {\n            handleAccSharedChange(accShared);\n        }\n    }, [\n        basePrice\n    ]);\n    // Handle addon price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        handleAddonChange(boosters, 0.4, setBoostersPrice);\n    }, [\n        boosters,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        handleAddonChange(priority, 0.2, setPriorityPrice);\n    }, [\n        priority,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        handleAddonChange(stream, 0.1, setStreamPrice);\n    }, [\n        stream,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        getPriceControl();\n    }, [\n        getPriceControl\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"col-span-12 \",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-12 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-9 bg-primary-dark rounded-md px-6 py-8 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center text-xl font-bold mr-4 text-white\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white font-montserrat\",\n                                                children: \"SELECT YOUR CURRENT AND DESIRED LEVEL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-4xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"CURRENT LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-yellow font-montserrat\",\n                                                                    children: currentLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel <= 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 552,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 547,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-yellow text-black px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: currentLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 554,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel >= 99,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 562,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"DESIRED LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-orange font-montserrat\",\n                                                                    children: desiredLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel <= currentLevel + 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 577,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-orange text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: desiredLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 579,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel >= 100,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 587,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiRangeSlider, {\n                                                    min: 1,\n                                                    max: 100,\n                                                    currentLevel: currentLevel,\n                                                    desiredLevel: desiredLevel,\n                                                    onCurrentChange: setCurrentLevel,\n                                                    onDesiredChange: setDesiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid items-center grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"ACC SHARED OR PLAY\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"W BOOSTERS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                options: LolLevelBoostingDropDown,\n                                                selectedValue: accShared,\n                                                onSelect: handleAccSharedChange,\n                                                placeholder: \"ACC SHARED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT ROLE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                options: roleOptions,\n                                                selectedValue: role,\n                                                onSelect: setRole,\n                                                placeholder: \"ANY ROLE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT PLATFORM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                options: platformOptions,\n                                                selectedValue: platform,\n                                                onSelect: setPlatform,\n                                                placeholder: \"PC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 528,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-3 bg-primary-dark rounded-md p-2 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-secondary-dark p-4 rounded-lg mb-6 flex items-center justify-center border border-secondary-dark\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400 mb-2 font-montserrat\",\n                                            children: \"BOOST FROM LEVEL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold mb-3 font-montserrat\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-yellow\",\n                                                    children: currentLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 mx-2\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-orange\",\n                                                    children: desiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-primary-yellow to-primary-orange text-white px-4 py-2 rounded-full text-sm font-bold font-montserrat\",\n                                            children: [\n                                                \"+\",\n                                                desiredLevel - currentLevel,\n                                                \" LEVELS\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Play with Boosters (+40%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: boosters,\n                                                        onChange: ()=>setBoosters(!boosters),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Appear Offline (Free)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: offline,\n                                                        onChange: ()=>setOffline(!offline),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Champions/Roles (Free)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: roles,\n                                                        onChange: ()=>setRoles(!roles),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Priority Order (+20%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: priority,\n                                                        onChange: ()=>setPriority(!priority),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Stream Games (+10%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: stream,\n                                                        onChange: ()=>setStream(!stream),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 676,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_discountCode__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                setDiscount: setDiscount,\n                                discount: discount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 744,\n                                columnNumber: 13\n                            }, undefined),\n                            discount < 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-medium text-gray-400\",\n                                                children: \"Original Price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-medium text-gray-400 line-through\",\n                                                children: [\n                                                    \"$\",\n                                                    (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-sm border border-green-500 p-3 flex justify-between items-center bg-[#1C1E2B]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-semibold text-green-400\",\n                                                children: [\n                                                    \"Total Amount (\",\n                                                    Math.round((1 - discount) * 100),\n                                                    \"% OFF)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-semibold text-green-400\",\n                                                children: [\n                                                    \"$\",\n                                                    (total === null || total === void 0 ? void 0 : total.price) || ((basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice) * discount).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 758,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 748,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-montserrat font-semibold\",\n                                        children: \"Total Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-montserrat font-semibold\",\n                                        children: [\n                                            \"$\",\n                                            (total === null || total === void 0 ? void 0 : total.price) || (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 768,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 766,\n                                columnNumber: 15\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                disabled: true,\n                                type: \"button\",\n                                className: \"flex justify-center items-center text-white bg-secondary-600 font-medium w-full rounded-lg text-sm me-2 mb-2 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_10__.Bars, {\n                                    height: \"40\",\n                                    color: \"white\",\n                                    ariaLabel: \"bars-loading\",\n                                    wrapperStyle: {},\n                                    wrapperClass: \"\",\n                                    visible: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 776,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleSubmit,\n                                className: \"text-white bg-primary-purple hover:bg-primary-shaded-purple focus:ring-4 w-full focus:ring-blue-300 font-medium rounded-lg text-sm px-7 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\",\n                                children: \"Level Up Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 791,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-xs text-gray-400 mt-4 font-montserrat\",\n                                children: [\n                                    \"Completion Time: \",\n                                    completionTime.days,\n                                    \" Days \",\n                                    completionTime.hours,\n                                    \" Hours\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 801,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 526,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n            lineNumber: 525,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 524,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(LevelBoostingComponent, \"6QjUyUuzW16cIsH+qP6AcXp4LIs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter\n    ];\n});\n_c1 = LevelBoostingComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LevelBoostingComponent);\nvar _c, _c1;\n$RefreshReg$(_c, \"MultiRangeSlider\");\n$RefreshReg$(_c1, \"LevelBoostingComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx\n"));

/***/ })

});