{"name": "boostcity-next", "version": "0.1.0", "private": true, "engines": {"node": ">=18.17.0", "npm": ">=8.3.1"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/supabase-adapter": "^0.1.13", "@emotion/react": "^11.11.1", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.1.1", "@mailchimp/mailchimp_marketing": "^3.0.80", "@mailchimp/mailchimp_transactional": "^1.0.59", "@nextui-org/react": "^2.2.9", "@nowpaymentsio/nowpayments-api-js": "^1.0.5", "@paypal/react-paypal-js": "^8.1.4", "@stripe/react-stripe-js": "^2.5.0", "@stripe/stripe-js": "^3.0.5", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.39.1", "@szhsin/react-accordion": "^1.2.3", "@types/mailchimp__mailchimp_transactional": "^1.0.10", "@vercel/analytics": "^1.2.2", "@vercel/speed-insights": "^1.0.2", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "flowbite": "^2.2.1", "framer-motion": "^10.16.16", "lucide-react": "^0.544.0", "mailchimp_transactional": "^0.0.1-security", "mailgen": "^2.0.28", "next": "14.0.3", "next-auth": "^4.24.5", "nodemailer": "^6.9.13", "react": "^18", "react-awesome-reveal": "^4.2.7", "react-circular-progressbar": "^2.1.0", "react-dom": "^18", "react-loader-spinner": "^6.1.6", "react-select": "^5.8.0", "react-slick": "^0.29.0", "react-toastify": "^9.1.3", "smtp-server": "^3.13.4", "stripe": "^14.18.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-slick": "^0.23.12", "@types/smtp-server": "^3.5.10", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.3", "eslint-plugin-unused-imports": "^3.0.0", "postcss": "^8", "tailwindcss": "^3.3.6", "typescript": "^5"}}