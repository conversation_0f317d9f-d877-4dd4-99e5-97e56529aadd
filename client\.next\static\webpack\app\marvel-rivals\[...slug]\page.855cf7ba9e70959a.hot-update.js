"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/marvel-rivals/[...slug]/page",{

/***/ "(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx":
/*!*******************************************************************!*\
  !*** ./app/marvel-rivals/[...slug]/components/level-boosting.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n// Custom Dropdown Component\nconst CustomDropDown = (param)=>{\n    let { options, placeholder, selectedValue, onSelect, className = \"\" } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-purple text-white px-6 py-3 rounded-lg cursor-pointer flex items-center justify-center min-w-[200px] hover:bg-primary-purple/80 transition-colors border border-secondary-dark\",\n                onClick: ()=>setIsOpen(!isOpen),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium font-montserrat\",\n                        children: (selectedValue === null || selectedValue === void 0 ? void 0 : selectedValue.label) || placeholder\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"ml-2 w-4 h-4\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-full left-0 right-0 bg-primary-dark-grey border border-secondary-dark rounded-lg mt-1 z-50 shadow-lg\",\n                children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 hover:bg-secondary-dark cursor-pointer text-white transition-colors font-montserrat\",\n                        onClick: ()=>{\n                            onSelect(option);\n                            setIsOpen(false);\n                        },\n                        children: option.label\n                    }, option.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomDropDown, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c = CustomDropDown;\n// Discount Code Component\nconst DiscountCodeComponent = (param)=>{\n    let { setDiscount, discount } = param;\n    _s1();\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [applied, setApplied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const applyDiscount = ()=>{\n        if (code.toLowerCase() === \"discount10\") {\n            setDiscount(0.9);\n            setApplied(true);\n        } else if (code.toLowerCase() === \"save25\") {\n            setDiscount(0.75);\n            setApplied(true);\n        } else {\n            alert('Invalid discount code. Try \"discount10\" or \"save25\"');\n        }\n    };\n    const removeDiscount = ()=>{\n        setDiscount(1);\n        setApplied(false);\n        setCode(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-4 p-3 bg-primary-dark-grey rounded-lg border border-secondary-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: code,\n                        onChange: (e)=>setCode(e.target.value),\n                        placeholder: \"Try: discount10 or save25\",\n                        className: \"flex-1 px-3 py-2 bg-secondary-dark border border-secondary-dark rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple font-montserrat\",\n                        disabled: applied\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: applied ? removeDiscount : applyDiscount,\n                        className: \"px-4 py-2 rounded font-medium transition-colors font-montserrat \".concat(applied ? \"bg-red-600 hover:bg-red-700 text-white\" : \"bg-primary-purple hover:bg-primary-purple/80 text-white\"),\n                        children: applied ? \"Remove\" : \"Apply\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            applied && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-green-400 text-sm mt-1 font-montserrat\",\n                children: [\n                    \"Discount applied successfully! \",\n                    Math.round((1 - discount) * 100),\n                    \"% off\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DiscountCodeComponent, \"a7bggQr9ThxF6zYom1UyTK7Cruc=\");\n_c1 = DiscountCodeComponent;\n// Multi-Range Slider Component\nconst MultiRangeSlider = (param)=>{\n    let { min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange } = param;\n    _s2();\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getPercent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((value)=>Math.round((value - min) / (max - min) * 100), [\n        min,\n        max\n    ]);\n    const handleMouseDown = (type)=>(event)=>{\n            setIsDragging(type);\n            event.preventDefault();\n        };\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        if (!isDragging || !sliderRef.current) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = Math.min(100, Math.max(0, (event.clientX - rect.left) / rect.width * 100));\n        const value = Math.round(min + percent / 100 * (max - min));\n        if (isDragging === \"current\") {\n            const newCurrent = Math.min(value, desiredLevel - 1);\n            onCurrentChange(Math.max(min, newCurrent));\n        } else if (isDragging === \"desired\") {\n            const newDesired = Math.max(value, currentLevel + 1);\n            onDesiredChange(Math.min(max, newDesired));\n        }\n    }, [\n        isDragging,\n        min,\n        max,\n        currentLevel,\n        desiredLevel,\n        onCurrentChange,\n        onDesiredChange\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setIsDragging(null);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    const currentPercent = getPercent(currentLevel);\n    const desiredPercent = getPercent(desiredLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-8 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-4\",\n                ref: sliderRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-6 bg-secondary-dark rounded-lg border border-secondary-dark\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute h-6 bg-gradient-to-r from-primary-yellow to-primary-orange rounded-lg transition-all duration-300\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                width: \"\".concat(desiredPercent - currentPercent, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-6 h-6 bg-primary-yellow rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-primary-yellow/80 z-20\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"current\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-primary-yellow text-black px-2 py-1 rounded text-xs font-bold whitespace-nowrap font-montserrat\",\n                                children: [\n                                    \"Current: \",\n                                    currentLevel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-6 h-6 bg-primary-orange rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-primary-orange/80 z-20\",\n                            style: {\n                                left: \"\".concat(desiredPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"desired\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-primary-orange text-white px-2 py-1 rounded text-xs font-bold whitespace-nowrap font-montserrat\",\n                                children: [\n                                    \"Target: \",\n                                    desiredLevel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between text-sm text-gray-400 mb-4 font-montserrat\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"START\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"25\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"LOW\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"MID\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"75\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"HIGH\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"100\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"MAX\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-primary-dark-grey rounded-lg border border-secondary-dark\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-yellow font-bold text-lg font-montserrat\",\n                                        children: currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"CURRENT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-purple font-bold text-lg font-montserrat\",\n                                        children: desiredLevel - currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"LEVELS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-orange font-bold text-lg font-montserrat\",\n                                        children: desiredLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"TARGET\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-400 font-bold font-montserrat\",\n                            children: [\n                                currentLevel,\n                                \" → \",\n                                desiredLevel,\n                                \" (\",\n                                desiredLevel - currentLevel,\n                                \" levels)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MultiRangeSlider, \"IBff4LxYZyFuRUNRi8ivWheMclo=\");\n_c2 = MultiRangeSlider;\nconst LevelBoostingComponent = ()=>{\n    _s3();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [discount, setDiscount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Level states\n    const [currentLevel, setCurrentLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(21);\n    const [desiredLevel, setDesiredLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(64);\n    // Mock data\n    const LolLevelBoostingDropDown = [\n        {\n            id: \"acc1\",\n            label: \"ACC SHARED\",\n            title: \"ACC SHARED\",\n            value: 0,\n            action: \"None\"\n        },\n        {\n            id: \"acc2\",\n            label: \"PLAY W BOOSTERS (100%)\",\n            title: \"PLAY W BOOSTERS (100%)\",\n            value: 100,\n            action: \"Increase\"\n        },\n        {\n            id: \"acc3\",\n            label: \"PLAY W BOOSTERS (200%)\",\n            title: \"PLAY W BOOSTERS (200%)\",\n            value: 200,\n            action: \"Increase\"\n        }\n    ];\n    const platformOptions = [\n        {\n            id: \"pc\",\n            label: \"PC\",\n            title: \"PC\"\n        },\n        {\n            id: \"console\",\n            label: \"CONSOLE\",\n            title: \"CONSOLE\"\n        }\n    ];\n    const roleOptions = [\n        {\n            id: \"any\",\n            label: \"ANY ROLE\",\n            title: \"ANY ROLE\"\n        },\n        {\n            id: \"tank\",\n            label: \"TANK\",\n            title: \"TANK\"\n        },\n        {\n            id: \"damage\",\n            label: \"DAMAGE\",\n            title: \"DAMAGE\"\n        },\n        {\n            id: \"support\",\n            label: \"SUPPORT\",\n            title: \"SUPPORT\"\n        }\n    ];\n    // Selection states\n    const [accShared, setAccShared] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(LolLevelBoostingDropDown[0]);\n    const [platform, setPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(platformOptions[0]);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(roleOptions[0]);\n    // Price states\n    const [totalPrice, setTotalPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [completionTime, setCompletionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        days: 0,\n        hours: 0\n    });\n    // Add-on states\n    const [boosters, setBoosters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [offline, setOffline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [priority, setPriority] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Level adjustment functions\n    const adjustCurrentLevel = (increment)=>{\n        setCurrentLevel((prev)=>{\n            const newLevel = Math.max(1, Math.min(99, prev + increment));\n            if (newLevel >= desiredLevel) {\n                setDesiredLevel(Math.min(100, newLevel + 1));\n            }\n            return newLevel;\n        });\n    };\n    const adjustDesiredLevel = (increment)=>{\n        setDesiredLevel((prev)=>{\n            const newLevel = Math.max(currentLevel + 1, Math.min(100, prev + increment));\n            return newLevel;\n        });\n    };\n    // Calculate price based on level difference\n    const calculateBasePrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return 5.21;\n        }\n        const maxPrice = 2681.80;\n        const minPrice = 5.21;\n        const priceRange = maxPrice - minPrice;\n        const calculatedPrice = minPrice + (levelDifference - 1) / 98 * priceRange;\n        return Math.max(minPrice, calculatedPrice);\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Calculate completion time\n    const calculateCompletionTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return {\n                days: 0,\n                hours: 1\n            };\n        }\n        const maxDays = 73;\n        const maxHours = 5;\n        const totalMaxHours = maxDays * 24 + maxHours;\n        const totalHours = levelDifference / 99 * totalMaxHours;\n        const days = Math.floor(totalHours / 24);\n        const hours = Math.round(totalHours % 24);\n        return {\n            days,\n            hours\n        };\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Calculate total price with add-ons\n    const calculateTotalPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const basePrice = calculateBasePrice();\n        let additionalPrice = 0;\n        if (accShared.label === \"PLAY W BOOSTERS (100%)\") {\n            additionalPrice += basePrice * 1.0;\n        } else if (accShared.label === \"PLAY W BOOSTERS (200%)\") {\n            additionalPrice += basePrice * 2.0;\n        }\n        if (boosters) additionalPrice += basePrice * 0.4;\n        if (priority) additionalPrice += basePrice * 0.2;\n        if (stream) additionalPrice += basePrice * 0.1;\n        const subtotal = basePrice + additionalPrice;\n        const finalPrice = subtotal * discount;\n        return finalPrice;\n    }, [\n        calculateBasePrice,\n        accShared.label,\n        boosters,\n        priority,\n        stream,\n        discount\n    ]);\n    // Handle order creation\n    const handleSubmit = async ()=>{\n        if (desiredLevel <= currentLevel) {\n            alert(\"Desired level must be higher than current level!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            alert(\"Order created successfully!\\nLevel: \".concat(currentLevel, \" → \").concat(desiredLevel, \"\\nTotal: $\").concat(totalPrice.toFixed(2)));\n        } catch (error) {\n            console.error(\"Order creation failed:\", error);\n            alert(\"Failed to create order. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const time = calculateCompletionTime();\n        setCompletionTime(time);\n    }, [\n        calculateCompletionTime\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const price = calculateTotalPrice();\n        setTotalPrice(price);\n    }, [\n        calculateTotalPrice\n    ]);\n    const originalPrice = calculateTotalPrice() / discount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"col-span-12 bg-primary-dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-12 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-9 bg-primary-dark-grey rounded-md px-6 py-8 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center text-xl font-bold mr-4 text-white\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white font-montserrat\",\n                                                children: \"SELECT YOUR CURRENT AND DESIRED LEVEL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-4xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"CURRENT LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-yellow font-montserrat\",\n                                                                    children: currentLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel <= 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 416,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-yellow text-black px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: currentLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel >= 99,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 426,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"DESIRED LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-orange font-montserrat\",\n                                                                    children: desiredLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel <= currentLevel + 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 441,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-orange text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: desiredLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 443,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel >= 100,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 451,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiRangeSlider, {\n                                                    min: 1,\n                                                    max: 100,\n                                                    currentLevel: currentLevel,\n                                                    desiredLevel: desiredLevel,\n                                                    onCurrentChange: setCurrentLevel,\n                                                    onDesiredChange: setDesiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid ce grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"ACC SHARED OR PLAY\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"W BOOSTERS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomDropDown, {\n                                                options: LolLevelBoostingDropDown,\n                                                selectedValue: accShared,\n                                                onSelect: setAccShared,\n                                                placeholder: \"ACC SHARED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT ROLE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomDropDown, {\n                                                options: roleOptions,\n                                                selectedValue: role,\n                                                onSelect: setRole,\n                                                placeholder: \"ANY ROLE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT PLATFORM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomDropDown, {\n                                                options: platformOptions,\n                                                selectedValue: platform,\n                                                onSelect: setPlatform,\n                                                placeholder: \"PC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-3 bg-primary-dark-grey rounded-md p-2 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-secondary-dark p-4 rounded-lg mb-6 flex items-center justify-center border border-secondary-dark\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400 mb-2 font-montserrat\",\n                                            children: \"BOOST FROM LEVEL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold mb-3 font-montserrat\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-yellow\",\n                                                    children: currentLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 mx-2\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-orange\",\n                                                    children: desiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-primary-yellow to-primary-orange text-white px-4 py-2 rounded-full text-sm font-bold font-montserrat\",\n                                            children: [\n                                                \"+\",\n                                                desiredLevel - currentLevel,\n                                                \" LEVELS\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-secondary-dark p-4 rounded-lg mb-6 border border-secondary-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-white font-montserrat\",\n                                            children: \"APPROXIMATE COMPLETION\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xl font-bold text-center text-white font-montserrat\",\n                                        children: [\n                                            completionTime.days > 0 && \"\".concat(completionTime.days, \" DAYS \"),\n                                            completionTime.hours,\n                                            \" HOURS\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 font-montserrat\",\n                                                children: \"ACTIVE BOOSTERS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-white font-montserrat\",\n                                                        children: \"61\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 pt-3 border-t border-gray-600 text-sm text-gray-400 font-montserrat\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Level Range:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-primary-orange\",\n                                                        children: [\n                                                            currentLevel,\n                                                            \" → \",\n                                                            desiredLevel\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Levels to Boost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-primary-purple\",\n                                                        children: desiredLevel - currentLevel\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Time per Level:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            desiredLevel > currentLevel ? ((completionTime.days * 24 + completionTime.hours) / (desiredLevel - currentLevel)).toFixed(1) : \"0\",\n                                                            \" hrs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 mb-6\",\n                                children: [\n                                    {\n                                        label: \"Play with Boosters (+40%)\",\n                                        state: boosters,\n                                        setter: setBoosters\n                                    },\n                                    {\n                                        label: \"Appear Offline (Free)\",\n                                        state: offline,\n                                        setter: setOffline\n                                    },\n                                    {\n                                        label: \"Champions/Roles (Free)\",\n                                        state: roles,\n                                        setter: setRoles\n                                    },\n                                    {\n                                        label: \"Priority Order (+20%)\",\n                                        state: priority,\n                                        setter: setPriority\n                                    },\n                                    {\n                                        label: \"Stream Games (+10%)\",\n                                        state: stream,\n                                        setter: setStream\n                                    }\n                                ].map((addon, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: addon.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: addon.state,\n                                                        onChange: ()=>addon.setter(!addon.state),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DiscountCodeComponent, {\n                                setDiscount: setDiscount,\n                                discount: discount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-secondary-dark p-4 rounded-lg mb-6 border border-secondary-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center text-xl font-bold mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-montserrat\",\n                                                children: \"Total Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    discount < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400 line-through mb-1 font-montserrat\",\n                                                        children: [\n                                                            \"Original: $\",\n                                                            originalPrice.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl text-green-400 font-montserrat\",\n                                                        children: [\n                                                            \"$\",\n                                                            totalPrice.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    discount < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-green-400 font-montserrat\",\n                                                        children: [\n                                                            \"You save: $\",\n                                                            (originalPrice - totalPrice).toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-4 border-t border-gray-600 text-sm text-gray-400 font-montserrat\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Base Price (\",\n                                                            currentLevel,\n                                                            \"→\",\n                                                            desiredLevel,\n                                                            \"):\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateBasePrice().toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            discount < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-red-400 mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Discount (\",\n                                                                    Math.round((1 - discount) * 100),\n                                                                    \"%):\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"-$\",\n                                                                    (originalPrice - totalPrice).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between font-bold text-green-400 text-base mt-2 pt-2 border-t border-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Final Price:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    totalPrice.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    desiredLevel === 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-gradient-to-r from-primary-orange/20 to-red-500/20 rounded-lg border border-primary-orange/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-primary-orange font-bold text-sm font-montserrat\",\n                                                    children: \"MAX LEVEL BOOST\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white text-xs mt-1 font-montserrat\",\n                                                    children: [\n                                                        \"Complete \",\n                                                        currentLevel,\n                                                        \"→100 journey in \",\n                                                        completionTime.days,\n                                                        \" days \",\n                                                        completionTime.hours,\n                                                        \" hours!\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                disabled: true,\n                                className: \"w-full bg-gray-600 text-white py-3 rounded-lg flex justify-center items-center font-montserrat\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSubmit,\n                                className: \"w-full bg-primary-purple hover:bg-primary-purple/80 text-white py-3 rounded-lg font-bold text-lg transition-colors font-montserrat\",\n                                children: \"Level Up Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-xs text-gray-400 mt-4 font-montserrat\",\n                                children: [\n                                    \"Completion Time: \",\n                                    completionTime.days,\n                                    \" Days \",\n                                    completionTime.hours,\n                                    \" Hours\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 390,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n            lineNumber: 389,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 388,\n        columnNumber: 5\n    }, undefined);\n};\n_s3(LevelBoostingComponent, \"I4xZ2l8//fbKv/5I/JTXl5HJEuo=\");\n_c3 = LevelBoostingComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LevelBoostingComponent);\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"CustomDropDown\");\n$RefreshReg$(_c1, \"DiscountCodeComponent\");\n$RefreshReg$(_c2, \"MultiRangeSlider\");\n$RefreshReg$(_c3, \"LevelBoostingComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx\n"));

/***/ })

});