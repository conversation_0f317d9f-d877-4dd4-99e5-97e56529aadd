"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-auth/client/_utils.js":
/*!*************************************************!*\
  !*** ./node_modules/next-auth/client/_utils.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.BroadcastChannel = BroadcastChannel;\nexports.apiBaseUrl = apiBaseUrl;\nexports.fetchData = fetchData;\nexports.now = now;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        enumerableOnly && (symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        })), keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = null != arguments[i] ? arguments[i] : {};\n        i % 2 ? ownKeys(Object(source), !0).forEach(function(key) {\n            (0, _defineProperty2.default)(target, key, source[key]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {\n            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n    }\n    return target;\n}\nfunction fetchData(_x, _x2, _x3) {\n    return _fetchData.apply(this, arguments);\n}\nfunction _fetchData() {\n    _fetchData = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(path, __NEXTAUTH, logger) {\n        var _ref, ctx, _ref$req, req, url, _req$headers, options, res, data, _args = arguments;\n        return _regenerator.default.wrap(function _callee$(_context) {\n            while(1){\n                switch(_context.prev = _context.next){\n                    case 0:\n                        _ref = _args.length > 3 && _args[3] !== undefined ? _args[3] : {}, ctx = _ref.ctx, _ref$req = _ref.req, req = _ref$req === void 0 ? ctx === null || ctx === void 0 ? void 0 : ctx.req : _ref$req;\n                        url = \"\".concat(apiBaseUrl(__NEXTAUTH), \"/\").concat(path);\n                        _context.prev = 2;\n                        options = {\n                            headers: _objectSpread({\n                                \"Content-Type\": \"application/json\"\n                            }, req !== null && req !== void 0 && (_req$headers = req.headers) !== null && _req$headers !== void 0 && _req$headers.cookie ? {\n                                cookie: req.headers.cookie\n                            } : {})\n                        };\n                        if (req !== null && req !== void 0 && req.body) {\n                            options.body = JSON.stringify(req.body);\n                            options.method = \"POST\";\n                        }\n                        _context.next = 7;\n                        return fetch(url, options);\n                    case 7:\n                        res = _context.sent;\n                        _context.next = 10;\n                        return res.json();\n                    case 10:\n                        data = _context.sent;\n                        if (res.ok) {\n                            _context.next = 13;\n                            break;\n                        }\n                        throw data;\n                    case 13:\n                        return _context.abrupt(\"return\", Object.keys(data).length > 0 ? data : null);\n                    case 16:\n                        _context.prev = 16;\n                        _context.t0 = _context[\"catch\"](2);\n                        logger.error(\"CLIENT_FETCH_ERROR\", {\n                            error: _context.t0,\n                            url: url\n                        });\n                        return _context.abrupt(\"return\", null);\n                    case 20:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }\n        }, _callee, null, [\n            [\n                2,\n                16\n            ]\n        ]);\n    }));\n    return _fetchData.apply(this, arguments);\n}\nfunction apiBaseUrl(__NEXTAUTH) {\n    if (true) {\n        return \"\".concat(__NEXTAUTH.baseUrlServer).concat(__NEXTAUTH.basePathServer);\n    }\n    return __NEXTAUTH.basePath;\n}\nfunction now() {\n    return Math.floor(Date.now() / 1000);\n}\nfunction BroadcastChannel() {\n    var name = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"nextauth.message\";\n    return {\n        receive: function receive(onReceive) {\n            var handler = function handler(event) {\n                var _event$newValue;\n                if (event.key !== name) return;\n                var message = JSON.parse((_event$newValue = event.newValue) !== null && _event$newValue !== void 0 ? _event$newValue : \"{}\");\n                if ((message === null || message === void 0 ? void 0 : message.event) !== \"session\" || !(message !== null && message !== void 0 && message.data)) return;\n                onReceive(message);\n            };\n            window.addEventListener(\"storage\", handler);\n            return function() {\n                return window.removeEventListener(\"storage\", handler);\n            };\n        },\n        post: function post(message) {\n            if (true) return;\n            try {\n                localStorage.setItem(name, JSON.stringify(_objectSpread(_objectSpread({}, message), {}, {\n                    timestamp: now()\n                })));\n            } catch (_unused) {}\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/client/_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/core/errors.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/core/errors.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.UnsupportedStrategy = exports.UnknownError = exports.OAuthCallbackError = exports.MissingSecret = exports.MissingAuthorize = exports.MissingAdapterMethods = exports.MissingAdapter = exports.MissingAPIRoute = exports.InvalidCallbackUrl = exports.AccountNotLinkedError = void 0;\nexports.adapterErrorHandler = adapterErrorHandler;\nexports.capitalize = capitalize;\nexports.eventsErrorHandler = eventsErrorHandler;\nexports.upperSnake = upperSnake;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _assertThisInitialized2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/assertThisInitialized */ \"(ssr)/./node_modules/@babel/runtime/helpers/assertThisInitialized.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/createClass.js\"));\nvar _inherits2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/inherits.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"(ssr)/./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"(ssr)/./node_modules/@babel/runtime/helpers/getPrototypeOf.js\"));\nvar _wrapNativeSuper2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/wrapNativeSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/wrapNativeSuper.js\"));\nfunction _createSuper(Derived) {\n    var hasNativeReflectConstruct = _isNativeReflectConstruct();\n    return function _createSuperInternal() {\n        var Super = (0, _getPrototypeOf2.default)(Derived), result;\n        if (hasNativeReflectConstruct) {\n            var NewTarget = (0, _getPrototypeOf2.default)(this).constructor;\n            result = Reflect.construct(Super, arguments, NewTarget);\n        } else {\n            result = Super.apply(this, arguments);\n        }\n        return (0, _possibleConstructorReturn2.default)(this, result);\n    };\n}\nfunction _isNativeReflectConstruct() {\n    if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n    if (Reflect.construct.sham) return false;\n    if (typeof Proxy === \"function\") return true;\n    try {\n        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n        return true;\n    } catch (e) {\n        return false;\n    }\n}\nvar UnknownError = function(_Error) {\n    (0, _inherits2.default)(UnknownError, _Error);\n    var _super = _createSuper(UnknownError);\n    function UnknownError(error) {\n        var _message;\n        var _this;\n        (0, _classCallCheck2.default)(this, UnknownError);\n        _this = _super.call(this, (_message = error === null || error === void 0 ? void 0 : error.message) !== null && _message !== void 0 ? _message : error);\n        _this.name = \"UnknownError\";\n        _this.code = error.code;\n        if (error instanceof Error) {\n            _this.stack = error.stack;\n        }\n        return _this;\n    }\n    (0, _createClass2.default)(UnknownError, [\n        {\n            key: \"toJSON\",\n            value: function toJSON() {\n                return {\n                    name: this.name,\n                    message: this.message,\n                    stack: this.stack\n                };\n            }\n        }\n    ]);\n    return UnknownError;\n}((0, _wrapNativeSuper2.default)(Error));\nexports.UnknownError = UnknownError;\nvar OAuthCallbackError = function(_UnknownError) {\n    (0, _inherits2.default)(OAuthCallbackError, _UnknownError);\n    var _super2 = _createSuper(OAuthCallbackError);\n    function OAuthCallbackError() {\n        var _this2;\n        (0, _classCallCheck2.default)(this, OAuthCallbackError);\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        _this2 = _super2.call.apply(_super2, [\n            this\n        ].concat(args));\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this2), \"name\", \"OAuthCallbackError\");\n        return _this2;\n    }\n    return (0, _createClass2.default)(OAuthCallbackError);\n}(UnknownError);\nexports.OAuthCallbackError = OAuthCallbackError;\nvar AccountNotLinkedError = function(_UnknownError2) {\n    (0, _inherits2.default)(AccountNotLinkedError, _UnknownError2);\n    var _super3 = _createSuper(AccountNotLinkedError);\n    function AccountNotLinkedError() {\n        var _this3;\n        (0, _classCallCheck2.default)(this, AccountNotLinkedError);\n        for(var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){\n            args[_key2] = arguments[_key2];\n        }\n        _this3 = _super3.call.apply(_super3, [\n            this\n        ].concat(args));\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this3), \"name\", \"AccountNotLinkedError\");\n        return _this3;\n    }\n    return (0, _createClass2.default)(AccountNotLinkedError);\n}(UnknownError);\nexports.AccountNotLinkedError = AccountNotLinkedError;\nvar MissingAPIRoute = function(_UnknownError3) {\n    (0, _inherits2.default)(MissingAPIRoute, _UnknownError3);\n    var _super4 = _createSuper(MissingAPIRoute);\n    function MissingAPIRoute() {\n        var _this4;\n        (0, _classCallCheck2.default)(this, MissingAPIRoute);\n        for(var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++){\n            args[_key3] = arguments[_key3];\n        }\n        _this4 = _super4.call.apply(_super4, [\n            this\n        ].concat(args));\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this4), \"name\", \"MissingAPIRouteError\");\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this4), \"code\", \"MISSING_NEXTAUTH_API_ROUTE_ERROR\");\n        return _this4;\n    }\n    return (0, _createClass2.default)(MissingAPIRoute);\n}(UnknownError);\nexports.MissingAPIRoute = MissingAPIRoute;\nvar MissingSecret = function(_UnknownError4) {\n    (0, _inherits2.default)(MissingSecret, _UnknownError4);\n    var _super5 = _createSuper(MissingSecret);\n    function MissingSecret() {\n        var _this5;\n        (0, _classCallCheck2.default)(this, MissingSecret);\n        for(var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++){\n            args[_key4] = arguments[_key4];\n        }\n        _this5 = _super5.call.apply(_super5, [\n            this\n        ].concat(args));\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this5), \"name\", \"MissingSecretError\");\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this5), \"code\", \"NO_SECRET\");\n        return _this5;\n    }\n    return (0, _createClass2.default)(MissingSecret);\n}(UnknownError);\nexports.MissingSecret = MissingSecret;\nvar MissingAuthorize = function(_UnknownError5) {\n    (0, _inherits2.default)(MissingAuthorize, _UnknownError5);\n    var _super6 = _createSuper(MissingAuthorize);\n    function MissingAuthorize() {\n        var _this6;\n        (0, _classCallCheck2.default)(this, MissingAuthorize);\n        for(var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++){\n            args[_key5] = arguments[_key5];\n        }\n        _this6 = _super6.call.apply(_super6, [\n            this\n        ].concat(args));\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this6), \"name\", \"MissingAuthorizeError\");\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this6), \"code\", \"CALLBACK_CREDENTIALS_HANDLER_ERROR\");\n        return _this6;\n    }\n    return (0, _createClass2.default)(MissingAuthorize);\n}(UnknownError);\nexports.MissingAuthorize = MissingAuthorize;\nvar MissingAdapter = function(_UnknownError6) {\n    (0, _inherits2.default)(MissingAdapter, _UnknownError6);\n    var _super7 = _createSuper(MissingAdapter);\n    function MissingAdapter() {\n        var _this7;\n        (0, _classCallCheck2.default)(this, MissingAdapter);\n        for(var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++){\n            args[_key6] = arguments[_key6];\n        }\n        _this7 = _super7.call.apply(_super7, [\n            this\n        ].concat(args));\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this7), \"name\", \"MissingAdapterError\");\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this7), \"code\", \"EMAIL_REQUIRES_ADAPTER_ERROR\");\n        return _this7;\n    }\n    return (0, _createClass2.default)(MissingAdapter);\n}(UnknownError);\nexports.MissingAdapter = MissingAdapter;\nvar MissingAdapterMethods = function(_UnknownError7) {\n    (0, _inherits2.default)(MissingAdapterMethods, _UnknownError7);\n    var _super8 = _createSuper(MissingAdapterMethods);\n    function MissingAdapterMethods() {\n        var _this8;\n        (0, _classCallCheck2.default)(this, MissingAdapterMethods);\n        for(var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++){\n            args[_key7] = arguments[_key7];\n        }\n        _this8 = _super8.call.apply(_super8, [\n            this\n        ].concat(args));\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this8), \"name\", \"MissingAdapterMethodsError\");\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this8), \"code\", \"MISSING_ADAPTER_METHODS_ERROR\");\n        return _this8;\n    }\n    return (0, _createClass2.default)(MissingAdapterMethods);\n}(UnknownError);\nexports.MissingAdapterMethods = MissingAdapterMethods;\nvar UnsupportedStrategy = function(_UnknownError8) {\n    (0, _inherits2.default)(UnsupportedStrategy, _UnknownError8);\n    var _super9 = _createSuper(UnsupportedStrategy);\n    function UnsupportedStrategy() {\n        var _this9;\n        (0, _classCallCheck2.default)(this, UnsupportedStrategy);\n        for(var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++){\n            args[_key8] = arguments[_key8];\n        }\n        _this9 = _super9.call.apply(_super9, [\n            this\n        ].concat(args));\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this9), \"name\", \"UnsupportedStrategyError\");\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this9), \"code\", \"CALLBACK_CREDENTIALS_JWT_ERROR\");\n        return _this9;\n    }\n    return (0, _createClass2.default)(UnsupportedStrategy);\n}(UnknownError);\nexports.UnsupportedStrategy = UnsupportedStrategy;\nvar InvalidCallbackUrl = function(_UnknownError9) {\n    (0, _inherits2.default)(InvalidCallbackUrl, _UnknownError9);\n    var _super10 = _createSuper(InvalidCallbackUrl);\n    function InvalidCallbackUrl() {\n        var _this10;\n        (0, _classCallCheck2.default)(this, InvalidCallbackUrl);\n        for(var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++){\n            args[_key9] = arguments[_key9];\n        }\n        _this10 = _super10.call.apply(_super10, [\n            this\n        ].concat(args));\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this10), \"name\", \"InvalidCallbackUrl\");\n        (0, _defineProperty2.default)((0, _assertThisInitialized2.default)(_this10), \"code\", \"INVALID_CALLBACK_URL_ERROR\");\n        return _this10;\n    }\n    return (0, _createClass2.default)(InvalidCallbackUrl);\n}(UnknownError);\nexports.InvalidCallbackUrl = InvalidCallbackUrl;\nfunction upperSnake(s) {\n    return s.replace(/([A-Z])/g, \"_$1\").toUpperCase();\n}\nfunction capitalize(s) {\n    return \"\".concat(s[0].toUpperCase()).concat(s.slice(1));\n}\nfunction eventsErrorHandler(methods, logger) {\n    return Object.keys(methods).reduce(function(acc, name) {\n        acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n            var method, _args = arguments;\n            return _regenerator.default.wrap(function _callee$(_context) {\n                while(1){\n                    switch(_context.prev = _context.next){\n                        case 0:\n                            _context.prev = 0;\n                            method = methods[name];\n                            _context.next = 4;\n                            return method.apply(void 0, _args);\n                        case 4:\n                            return _context.abrupt(\"return\", _context.sent);\n                        case 7:\n                            _context.prev = 7;\n                            _context.t0 = _context[\"catch\"](0);\n                            logger.error(\"\".concat(upperSnake(name), \"_EVENT_ERROR\"), _context.t0);\n                        case 10:\n                        case \"end\":\n                            return _context.stop();\n                    }\n                }\n            }, _callee, null, [\n                [\n                    0,\n                    7\n                ]\n            ]);\n        }));\n        return acc;\n    }, {});\n}\nfunction adapterErrorHandler(adapter, logger) {\n    if (!adapter) return;\n    return Object.keys(adapter).reduce(function(acc, name) {\n        acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n            var _len10, args, _key10, method, e, _args2 = arguments;\n            return _regenerator.default.wrap(function _callee2$(_context2) {\n                while(1){\n                    switch(_context2.prev = _context2.next){\n                        case 0:\n                            _context2.prev = 0;\n                            for(_len10 = _args2.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++){\n                                args[_key10] = _args2[_key10];\n                            }\n                            logger.debug(\"adapter_\".concat(name), {\n                                args: args\n                            });\n                            method = adapter[name];\n                            _context2.next = 6;\n                            return method.apply(void 0, args);\n                        case 6:\n                            return _context2.abrupt(\"return\", _context2.sent);\n                        case 9:\n                            _context2.prev = 9;\n                            _context2.t0 = _context2[\"catch\"](0);\n                            logger.error(\"adapter_error_\".concat(name), _context2.t0);\n                            e = new UnknownError(_context2.t0);\n                            e.name = \"\".concat(capitalize(name), \"Error\");\n                            throw e;\n                        case 15:\n                        case \"end\":\n                            return _context2.stop();\n                    }\n                }\n            }, _callee2, null, [\n                [\n                    0,\n                    9\n                ]\n            ]);\n        }));\n        return acc;\n    }, {});\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/core/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/react/index.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/react/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nvar _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/typeof.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _exportNames = {\n    SessionContext: true,\n    useSession: true,\n    getSession: true,\n    getCsrfToken: true,\n    getProviders: true,\n    signIn: true,\n    signOut: true,\n    SessionProvider: true\n};\nexports.SessionContext = void 0;\nexports.SessionProvider = SessionProvider;\nexports.getCsrfToken = getCsrfToken;\nexports.getProviders = getProviders;\nexports.getSession = getSession;\nexports.signIn = signIn;\nexports.signOut = signOut;\nexports.useSession = useSession;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/slicedToArray.js\"));\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar _logger2 = _interopRequireWildcard(__webpack_require__(/*! ../utils/logger */ \"(ssr)/./node_modules/next-auth/utils/logger.js\"));\nvar _parseUrl = _interopRequireDefault(__webpack_require__(/*! ../utils/parse-url */ \"(ssr)/./node_modules/next-auth/utils/parse-url.js\"));\nvar _utils = __webpack_require__(/*! ../client/_utils */ \"(ssr)/./node_modules/next-auth/client/_utils.js\");\nvar _jsxRuntime = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nvar _types = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/next-auth/react/types.js\");\nObject.keys(_types).forEach(function(key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _types[key]) return;\n    Object.defineProperty(exports, key, {\n        enumerable: true,\n        get: function get() {\n            return _types[key];\n        }\n    });\n});\nvar _process$env$NEXTAUTH, _ref, _process$env$NEXTAUTH2, _process$env$NEXTAUTH3, _React$createContext;\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        enumerableOnly && (symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        })), keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = null != arguments[i] ? arguments[i] : {};\n        i % 2 ? ownKeys(Object(source), !0).forEach(function(key) {\n            (0, _defineProperty2.default)(target, key, source[key]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {\n            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n    }\n    return target;\n}\nvar __NEXTAUTH = {\n    baseUrl: (0, _parseUrl.default)((_process$env$NEXTAUTH = process.env.NEXTAUTH_URL) !== null && _process$env$NEXTAUTH !== void 0 ? _process$env$NEXTAUTH : process.env.VERCEL_URL).origin,\n    basePath: (0, _parseUrl.default)(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: (0, _parseUrl.default)((_ref = (_process$env$NEXTAUTH2 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process$env$NEXTAUTH2 !== void 0 ? _process$env$NEXTAUTH2 : process.env.NEXTAUTH_URL) !== null && _ref !== void 0 ? _ref : process.env.VERCEL_URL).origin,\n    basePathServer: (0, _parseUrl.default)((_process$env$NEXTAUTH3 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process$env$NEXTAUTH3 !== void 0 ? _process$env$NEXTAUTH3 : process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: function _getSession() {}\n};\nvar broadcast = (0, _utils.BroadcastChannel)();\nvar logger = (0, _logger2.proxyLogger)(_logger2.default, __NEXTAUTH.basePath);\nfunction useOnline() {\n    var _React$useState = React.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false), _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2), isOnline = _React$useState2[0], setIsOnline = _React$useState2[1];\n    var setOnline = function setOnline() {\n        return setIsOnline(true);\n    };\n    var setOffline = function setOffline() {\n        return setIsOnline(false);\n    };\n    React.useEffect(function() {\n        window.addEventListener(\"online\", setOnline);\n        window.addEventListener(\"offline\", setOffline);\n        return function() {\n            window.removeEventListener(\"online\", setOnline);\n            window.removeEventListener(\"offline\", setOffline);\n        };\n    }, []);\n    return isOnline;\n}\nvar SessionContext = (_React$createContext = React.createContext) === null || _React$createContext === void 0 ? void 0 : _React$createContext.call(React, undefined);\nexports.SessionContext = SessionContext;\nfunction useSession(options) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    var value = React.useContext(SessionContext);\n    if (!value && \"development\" !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    var _ref2 = options !== null && options !== void 0 ? options : {}, required = _ref2.required, onUnauthenticated = _ref2.onUnauthenticated;\n    var requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    React.useEffect(function() {\n        if (requiredAndNotLoading) {\n            var url = \"/api/auth/signin?\".concat(new URLSearchParams({\n                error: \"SessionRequired\",\n                callbackUrl: window.location.href\n            }));\n            if (onUnauthenticated) onUnauthenticated();\n            else window.location.href = url;\n        }\n    }, [\n        requiredAndNotLoading,\n        onUnauthenticated\n    ]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\"\n        };\n    }\n    return value;\n}\nfunction getSession(_x) {\n    return _getSession2.apply(this, arguments);\n}\nfunction _getSession2() {\n    _getSession2 = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee3(params) {\n        var _params$broadcast;\n        var session;\n        return _regenerator.default.wrap(function _callee3$(_context3) {\n            while(1){\n                switch(_context3.prev = _context3.next){\n                    case 0:\n                        _context3.next = 2;\n                        return (0, _utils.fetchData)(\"session\", __NEXTAUTH, logger, params);\n                    case 2:\n                        session = _context3.sent;\n                        if ((_params$broadcast = params === null || params === void 0 ? void 0 : params.broadcast) !== null && _params$broadcast !== void 0 ? _params$broadcast : true) {\n                            broadcast.post({\n                                event: \"session\",\n                                data: {\n                                    trigger: \"getSession\"\n                                }\n                            });\n                        }\n                        return _context3.abrupt(\"return\", session);\n                    case 5:\n                    case \"end\":\n                        return _context3.stop();\n                }\n            }\n        }, _callee3);\n    }));\n    return _getSession2.apply(this, arguments);\n}\nfunction getCsrfToken(_x2) {\n    return _getCsrfToken.apply(this, arguments);\n}\nfunction _getCsrfToken() {\n    _getCsrfToken = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee4(params) {\n        var response;\n        return _regenerator.default.wrap(function _callee4$(_context4) {\n            while(1){\n                switch(_context4.prev = _context4.next){\n                    case 0:\n                        _context4.next = 2;\n                        return (0, _utils.fetchData)(\"csrf\", __NEXTAUTH, logger, params);\n                    case 2:\n                        response = _context4.sent;\n                        return _context4.abrupt(\"return\", response === null || response === void 0 ? void 0 : response.csrfToken);\n                    case 4:\n                    case \"end\":\n                        return _context4.stop();\n                }\n            }\n        }, _callee4);\n    }));\n    return _getCsrfToken.apply(this, arguments);\n}\nfunction getProviders() {\n    return _getProviders.apply(this, arguments);\n}\nfunction _getProviders() {\n    _getProviders = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee5() {\n        return _regenerator.default.wrap(function _callee5$(_context5) {\n            while(1){\n                switch(_context5.prev = _context5.next){\n                    case 0:\n                        _context5.next = 2;\n                        return (0, _utils.fetchData)(\"providers\", __NEXTAUTH, logger);\n                    case 2:\n                        return _context5.abrupt(\"return\", _context5.sent);\n                    case 3:\n                    case \"end\":\n                        return _context5.stop();\n                }\n            }\n        }, _callee5);\n    }));\n    return _getProviders.apply(this, arguments);\n}\nfunction signIn(_x3, _x4, _x5) {\n    return _signIn.apply(this, arguments);\n}\nfunction _signIn() {\n    _signIn = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee6(provider, options, authorizationParams) {\n        var _ref5, _ref5$callbackUrl, callbackUrl, _ref5$redirect, redirect, baseUrl, providers, isCredentials, isEmail, isSupportingReturn, signInUrl, _signInUrl, res, data, _data$url, url, error;\n        return _regenerator.default.wrap(function _callee6$(_context6) {\n            while(1){\n                switch(_context6.prev = _context6.next){\n                    case 0:\n                        _ref5 = options !== null && options !== void 0 ? options : {}, _ref5$callbackUrl = _ref5.callbackUrl, callbackUrl = _ref5$callbackUrl === void 0 ? window.location.href : _ref5$callbackUrl, _ref5$redirect = _ref5.redirect, redirect = _ref5$redirect === void 0 ? true : _ref5$redirect;\n                        baseUrl = (0, _utils.apiBaseUrl)(__NEXTAUTH);\n                        _context6.next = 4;\n                        return getProviders();\n                    case 4:\n                        providers = _context6.sent;\n                        if (providers) {\n                            _context6.next = 8;\n                            break;\n                        }\n                        window.location.href = \"\".concat(baseUrl, \"/error\");\n                        return _context6.abrupt(\"return\");\n                    case 8:\n                        if (!(!provider || !(provider in providers))) {\n                            _context6.next = 11;\n                            break;\n                        }\n                        window.location.href = \"\".concat(baseUrl, \"/signin?\").concat(new URLSearchParams({\n                            callbackUrl: callbackUrl\n                        }));\n                        return _context6.abrupt(\"return\");\n                    case 11:\n                        isCredentials = providers[provider].type === \"credentials\";\n                        isEmail = providers[provider].type === \"email\";\n                        isSupportingReturn = isCredentials || isEmail;\n                        signInUrl = \"\".concat(baseUrl, \"/\").concat(isCredentials ? \"callback\" : \"signin\", \"/\").concat(provider);\n                        _signInUrl = \"\".concat(signInUrl).concat(authorizationParams ? \"?\".concat(new URLSearchParams(authorizationParams)) : \"\");\n                        _context6.t0 = fetch;\n                        _context6.t1 = _signInUrl;\n                        _context6.t2 = {\n                            \"Content-Type\": \"application/x-www-form-urlencoded\"\n                        };\n                        _context6.t3 = URLSearchParams;\n                        _context6.t4 = _objectSpread;\n                        _context6.t5 = _objectSpread({}, options);\n                        _context6.t6 = {};\n                        _context6.next = 25;\n                        return getCsrfToken();\n                    case 25:\n                        _context6.t7 = _context6.sent;\n                        _context6.t8 = callbackUrl;\n                        _context6.t9 = {\n                            csrfToken: _context6.t7,\n                            callbackUrl: _context6.t8,\n                            json: true\n                        };\n                        _context6.t10 = (0, _context6.t4)(_context6.t5, _context6.t6, _context6.t9);\n                        _context6.t11 = new _context6.t3(_context6.t10);\n                        _context6.t12 = {\n                            method: \"post\",\n                            headers: _context6.t2,\n                            body: _context6.t11\n                        };\n                        _context6.next = 33;\n                        return (0, _context6.t0)(_context6.t1, _context6.t12);\n                    case 33:\n                        res = _context6.sent;\n                        _context6.next = 36;\n                        return res.json();\n                    case 36:\n                        data = _context6.sent;\n                        if (!(redirect || !isSupportingReturn)) {\n                            _context6.next = 42;\n                            break;\n                        }\n                        url = (_data$url = data.url) !== null && _data$url !== void 0 ? _data$url : callbackUrl;\n                        window.location.href = url;\n                        if (url.includes(\"#\")) window.location.reload();\n                        return _context6.abrupt(\"return\");\n                    case 42:\n                        error = new URL(data.url).searchParams.get(\"error\");\n                        if (!res.ok) {\n                            _context6.next = 46;\n                            break;\n                        }\n                        _context6.next = 46;\n                        return __NEXTAUTH._getSession({\n                            event: \"storage\"\n                        });\n                    case 46:\n                        return _context6.abrupt(\"return\", {\n                            error: error,\n                            status: res.status,\n                            ok: res.ok,\n                            url: error ? null : data.url\n                        });\n                    case 47:\n                    case \"end\":\n                        return _context6.stop();\n                }\n            }\n        }, _callee6);\n    }));\n    return _signIn.apply(this, arguments);\n}\nfunction signOut(_x6) {\n    return _signOut.apply(this, arguments);\n}\nfunction _signOut() {\n    _signOut = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee7(options) {\n        var _options$redirect;\n        var _ref6, _ref6$callbackUrl, callbackUrl, baseUrl, fetchOptions, res, data, _data$url2, url;\n        return _regenerator.default.wrap(function _callee7$(_context7) {\n            while(1){\n                switch(_context7.prev = _context7.next){\n                    case 0:\n                        _ref6 = options !== null && options !== void 0 ? options : {}, _ref6$callbackUrl = _ref6.callbackUrl, callbackUrl = _ref6$callbackUrl === void 0 ? window.location.href : _ref6$callbackUrl;\n                        baseUrl = (0, _utils.apiBaseUrl)(__NEXTAUTH);\n                        _context7.t0 = {\n                            \"Content-Type\": \"application/x-www-form-urlencoded\"\n                        };\n                        _context7.t1 = URLSearchParams;\n                        _context7.next = 6;\n                        return getCsrfToken();\n                    case 6:\n                        _context7.t2 = _context7.sent;\n                        _context7.t3 = callbackUrl;\n                        _context7.t4 = {\n                            csrfToken: _context7.t2,\n                            callbackUrl: _context7.t3,\n                            json: true\n                        };\n                        _context7.t5 = new _context7.t1(_context7.t4);\n                        fetchOptions = {\n                            method: \"post\",\n                            headers: _context7.t0,\n                            body: _context7.t5\n                        };\n                        _context7.next = 13;\n                        return fetch(\"\".concat(baseUrl, \"/signout\"), fetchOptions);\n                    case 13:\n                        res = _context7.sent;\n                        _context7.next = 16;\n                        return res.json();\n                    case 16:\n                        data = _context7.sent;\n                        broadcast.post({\n                            event: \"session\",\n                            data: {\n                                trigger: \"signout\"\n                            }\n                        });\n                        if (!((_options$redirect = options === null || options === void 0 ? void 0 : options.redirect) !== null && _options$redirect !== void 0 ? _options$redirect : true)) {\n                            _context7.next = 23;\n                            break;\n                        }\n                        url = (_data$url2 = data.url) !== null && _data$url2 !== void 0 ? _data$url2 : callbackUrl;\n                        window.location.href = url;\n                        if (url.includes(\"#\")) window.location.reload();\n                        return _context7.abrupt(\"return\");\n                    case 23:\n                        _context7.next = 25;\n                        return __NEXTAUTH._getSession({\n                            event: \"storage\"\n                        });\n                    case 25:\n                        return _context7.abrupt(\"return\", data);\n                    case 26:\n                    case \"end\":\n                        return _context7.stop();\n                }\n            }\n        }, _callee7);\n    }));\n    return _signOut.apply(this, arguments);\n}\nfunction SessionProvider(props) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    var children = props.children, basePath = props.basePath, refetchInterval = props.refetchInterval, refetchWhenOffline = props.refetchWhenOffline;\n    if (basePath) __NEXTAUTH.basePath = basePath;\n    var hasInitialSession = props.session !== undefined;\n    __NEXTAUTH._lastSync = hasInitialSession ? (0, _utils.now)() : 0;\n    var _React$useState3 = React.useState(function() {\n        if (hasInitialSession) __NEXTAUTH._session = props.session;\n        return props.session;\n    }), _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2), session = _React$useState4[0], setSession = _React$useState4[1];\n    var _React$useState5 = React.useState(!hasInitialSession), _React$useState6 = (0, _slicedToArray2.default)(_React$useState5, 2), loading = _React$useState6[0], setLoading = _React$useState6[1];\n    React.useEffect(function() {\n        __NEXTAUTH._getSession = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n            var _ref4, event, storageEvent, _args = arguments;\n            return _regenerator.default.wrap(function _callee$(_context) {\n                while(1){\n                    switch(_context.prev = _context.next){\n                        case 0:\n                            _ref4 = _args.length > 0 && _args[0] !== undefined ? _args[0] : {}, event = _ref4.event;\n                            _context.prev = 1;\n                            storageEvent = event === \"storage\";\n                            if (!(storageEvent || __NEXTAUTH._session === undefined)) {\n                                _context.next = 10;\n                                break;\n                            }\n                            __NEXTAUTH._lastSync = (0, _utils.now)();\n                            _context.next = 7;\n                            return getSession({\n                                broadcast: !storageEvent\n                            });\n                        case 7:\n                            __NEXTAUTH._session = _context.sent;\n                            setSession(__NEXTAUTH._session);\n                            return _context.abrupt(\"return\");\n                        case 10:\n                            if (!(!event || __NEXTAUTH._session === null || (0, _utils.now)() < __NEXTAUTH._lastSync)) {\n                                _context.next = 12;\n                                break;\n                            }\n                            return _context.abrupt(\"return\");\n                        case 12:\n                            __NEXTAUTH._lastSync = (0, _utils.now)();\n                            _context.next = 15;\n                            return getSession();\n                        case 15:\n                            __NEXTAUTH._session = _context.sent;\n                            setSession(__NEXTAUTH._session);\n                            _context.next = 22;\n                            break;\n                        case 19:\n                            _context.prev = 19;\n                            _context.t0 = _context[\"catch\"](1);\n                            logger.error(\"CLIENT_SESSION_ERROR\", _context.t0);\n                        case 22:\n                            _context.prev = 22;\n                            setLoading(false);\n                            return _context.finish(22);\n                        case 25:\n                        case \"end\":\n                            return _context.stop();\n                    }\n                }\n            }, _callee, null, [\n                [\n                    1,\n                    19,\n                    22,\n                    25\n                ]\n            ]);\n        }));\n        __NEXTAUTH._getSession();\n        return function() {\n            __NEXTAUTH._lastSync = 0;\n            __NEXTAUTH._session = undefined;\n            __NEXTAUTH._getSession = function() {};\n        };\n    }, []);\n    React.useEffect(function() {\n        var unsubscribe = broadcast.receive(function() {\n            return __NEXTAUTH._getSession({\n                event: \"storage\"\n            });\n        });\n        return function() {\n            return unsubscribe();\n        };\n    }, []);\n    React.useEffect(function() {\n        var _props$refetchOnWindo = props.refetchOnWindowFocus, refetchOnWindowFocus = _props$refetchOnWindo === void 0 ? true : _props$refetchOnWindo;\n        var visibilityHandler = function visibilityHandler() {\n            if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n                event: \"visibilitychange\"\n            });\n        };\n        document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n        return function() {\n            return document.removeEventListener(\"visibilitychange\", visibilityHandler, false);\n        };\n    }, [\n        props.refetchOnWindowFocus\n    ]);\n    var isOnline = useOnline();\n    var shouldRefetch = refetchWhenOffline !== false || isOnline;\n    React.useEffect(function() {\n        if (refetchInterval && shouldRefetch) {\n            var refetchIntervalTimer = setInterval(function() {\n                if (__NEXTAUTH._session) {\n                    __NEXTAUTH._getSession({\n                        event: \"poll\"\n                    });\n                }\n            }, refetchInterval * 1000);\n            return function() {\n                return clearInterval(refetchIntervalTimer);\n            };\n        }\n    }, [\n        refetchInterval,\n        shouldRefetch\n    ]);\n    var value = React.useMemo(function() {\n        return {\n            data: session,\n            status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n            update: function update(data) {\n                return (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n                    var newSession;\n                    return _regenerator.default.wrap(function _callee2$(_context2) {\n                        while(1){\n                            switch(_context2.prev = _context2.next){\n                                case 0:\n                                    if (!(loading || !session)) {\n                                        _context2.next = 2;\n                                        break;\n                                    }\n                                    return _context2.abrupt(\"return\");\n                                case 2:\n                                    setLoading(true);\n                                    _context2.t0 = _utils.fetchData;\n                                    _context2.t1 = __NEXTAUTH;\n                                    _context2.t2 = logger;\n                                    _context2.next = 8;\n                                    return getCsrfToken();\n                                case 8:\n                                    _context2.t3 = _context2.sent;\n                                    _context2.t4 = data;\n                                    _context2.t5 = {\n                                        csrfToken: _context2.t3,\n                                        data: _context2.t4\n                                    };\n                                    _context2.t6 = {\n                                        body: _context2.t5\n                                    };\n                                    _context2.t7 = {\n                                        req: _context2.t6\n                                    };\n                                    _context2.next = 15;\n                                    return (0, _context2.t0)(\"session\", _context2.t1, _context2.t2, _context2.t7);\n                                case 15:\n                                    newSession = _context2.sent;\n                                    setLoading(false);\n                                    if (newSession) {\n                                        setSession(newSession);\n                                        broadcast.post({\n                                            event: \"session\",\n                                            data: {\n                                                trigger: \"getSession\"\n                                            }\n                                        });\n                                    }\n                                    return _context2.abrupt(\"return\", newSession);\n                                case 19:\n                                case \"end\":\n                                    return _context2.stop();\n                            }\n                        }\n                    }, _callee2);\n                }))();\n            }\n        };\n    }, [\n        session,\n        loading\n    ]);\n    return (0, _jsxRuntime.jsx)(SessionContext.Provider, {\n        value: value,\n        children: children\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/react/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/react/types.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/react/types.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3JlYWN0L3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBRWJBLDhDQUE2QztJQUMzQ0csT0FBTztBQUNULENBQUMsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Jvb3N0Y2l0eS1uZXh0Ly4vbm9kZV9tb2R1bGVzL25leHQtYXV0aC9yZWFjdC90eXBlcy5qcz8xZTVhIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pOyJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/react/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/utils/logger.js":
/*!************************************************!*\
  !*** ./node_modules/next-auth/utils/logger.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = void 0;\nexports.proxyLogger = proxyLogger;\nexports.setLogger = setLogger;\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _errors = __webpack_require__(/*! ../core/errors */ \"(ssr)/./node_modules/next-auth/core/errors.js\");\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        enumerableOnly && (symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        })), keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = null != arguments[i] ? arguments[i] : {};\n        i % 2 ? ownKeys(Object(source), !0).forEach(function(key) {\n            (0, _defineProperty2.default)(target, key, source[key]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {\n            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n    }\n    return target;\n}\nfunction formatError(o) {\n    if (o instanceof Error && !(o instanceof _errors.UnknownError)) {\n        return {\n            message: o.message,\n            stack: o.stack,\n            name: o.name\n        };\n    }\n    if (hasErrorProperty(o)) {\n        var _o$message;\n        o.error = formatError(o.error);\n        o.message = (_o$message = o.message) !== null && _o$message !== void 0 ? _o$message : o.error.message;\n    }\n    return o;\n}\nfunction hasErrorProperty(x) {\n    return !!(x !== null && x !== void 0 && x.error);\n}\nvar _logger = {\n    error: function error(code, metadata) {\n        metadata = formatError(metadata);\n        console.error(\"[next-auth][error][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/errors#\".concat(code.toLowerCase()), metadata.message, metadata);\n    },\n    warn: function warn(code) {\n        console.warn(\"[next-auth][warn][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/warnings#\".concat(code.toLowerCase()));\n    },\n    debug: function debug(code, metadata) {\n        console.log(\"[next-auth][debug][\".concat(code, \"]\"), metadata);\n    }\n};\nfunction setLogger() {\n    var newLogger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var debug = arguments.length > 1 ? arguments[1] : undefined;\n    if (!debug) _logger.debug = function() {};\n    if (newLogger.error) _logger.error = newLogger.error;\n    if (newLogger.warn) _logger.warn = newLogger.warn;\n    if (newLogger.debug) _logger.debug = newLogger.debug;\n}\nvar _default = _logger;\nexports[\"default\"] = _default;\nfunction proxyLogger() {\n    var logger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _logger;\n    var basePath = arguments.length > 1 ? arguments[1] : undefined;\n    try {\n        if (true) {\n            return logger;\n        }\n        var clientLogger = {};\n        var _loop = function _loop(level) {\n            clientLogger[level] = function(code, metadata) {\n                _logger[level](code, metadata);\n                if (level === \"error\") {\n                    metadata = formatError(metadata);\n                }\n                ;\n                metadata.client = true;\n                var url = \"\".concat(basePath, \"/_log\");\n                var body = new URLSearchParams(_objectSpread({\n                    level: level,\n                    code: code\n                }, metadata));\n                if (navigator.sendBeacon) {\n                    return navigator.sendBeacon(url, body);\n                }\n                return fetch(url, {\n                    method: \"POST\",\n                    body: body,\n                    keepalive: true\n                });\n            };\n        };\n        for(var level in logger){\n            _loop(level);\n        }\n        return clientLogger;\n    } catch (_unused) {\n        return _logger;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/utils/logger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/utils/parse-url.js":
/*!***************************************************!*\
  !*** ./node_modules/next-auth/utils/parse-url.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = parseUrl;\nfunction parseUrl(url) {\n    var _url2;\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = `https://${url}`;\n    }\n    const _url = new URL((_url2 = url) !== null && _url2 !== void 0 ? _url2 : defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname).replace(/\\/$/, \"\");\n    const base = `${_url.origin}${path}`;\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: ()=>base\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/utils/parse-url.js\n");

/***/ })

};
;