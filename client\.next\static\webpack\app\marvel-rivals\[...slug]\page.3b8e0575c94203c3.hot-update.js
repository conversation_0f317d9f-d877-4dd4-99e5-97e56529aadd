"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/marvel-rivals/[...slug]/page",{

/***/ "(app-pages-browser)/./app/marvel-rivals/[...slug]/components/rank-boosting.tsx":
/*!******************************************************************!*\
  !*** ./app/marvel-rivals/[...slug]/components/rank-boosting.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_custom_dropdown__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/custom-dropdown */ \"(app-pages-browser)/./components/custom-dropdown.tsx\");\n/* harmony import */ var _components_custom_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom-image */ \"(app-pages-browser)/./components/custom-image.tsx\");\n/* harmony import */ var _components_discountCode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/discountCode */ \"(app-pages-browser)/./components/discountCode.tsx\");\n/* harmony import */ var _components_division_grid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/division-grid */ \"(app-pages-browser)/./components/division-grid.tsx\");\n/* harmony import */ var _components_ranks_grid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ranks-grid */ \"(app-pages-browser)/./components/ranks-grid.tsx\");\n/* harmony import */ var _constants_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/constants/common */ \"(app-pages-browser)/./constants/common.ts\");\n/* harmony import */ var _constants_lol_constants__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/constants/lol-constants */ \"(app-pages-browser)/./constants/lol-constants.ts\");\n/* harmony import */ var _constants_service_types__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/constants/service-types */ \"(app-pages-browser)/./constants/service-types.ts\");\n/* harmony import */ var _database_functions_database__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/database/functions.database */ \"(app-pages-browser)/./database/functions.database.ts\");\n/* harmony import */ var _utils_now_payments__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/now-payments */ \"(app-pages-browser)/./utils/now-payments.ts\");\n/* harmony import */ var _utils_toast_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/toast-utils */ \"(app-pages-browser)/./utils/toast-utils.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-loader-spinner */ \"(app-pages-browser)/./node_modules/react-loader-spinner/dist/module.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Updated rank structure with new tiers\nconst updatedRanks = [\n    {\n        id: \"rank1\",\n        title: \"Bronze\",\n        image: \"/images/games/lol/Bronze.png\",\n        rank: _constants_lol_constants__WEBPACK_IMPORTED_MODULE_7__.MavelRankNames.Bronze,\n        accept: true\n    },\n    {\n        id: \"rank2\",\n        title: \"Silver\",\n        image: \"/images/games/lol/Silver.png\",\n        rank: _constants_lol_constants__WEBPACK_IMPORTED_MODULE_7__.MavelRankNames.Silver,\n        accept: true\n    },\n    {\n        id: \"rank3\",\n        title: \"Gold\",\n        image: \"/images/games/lol/Gold.png\",\n        rank: _constants_lol_constants__WEBPACK_IMPORTED_MODULE_7__.MavelRankNames.Gold,\n        accept: true\n    },\n    {\n        id: \"rank4\",\n        title: \"Platinum\",\n        image: \"/images/games/lol/Platinum.png\",\n        rank: _constants_lol_constants__WEBPACK_IMPORTED_MODULE_7__.MavelRankNames.Platinum,\n        accept: true\n    },\n    {\n        id: \"rank5\",\n        title: \"Diamond\",\n        image: \"/images/games/lol/Diamond.png\",\n        rank: _constants_lol_constants__WEBPACK_IMPORTED_MODULE_7__.MavelRankNames.Diamond,\n        accept: true\n    },\n    {\n        id: \"rank6\",\n        title: \"Master\",\n        image: \"/images/games/lol/Master.png\",\n        rank: _constants_lol_constants__WEBPACK_IMPORTED_MODULE_7__.MavelRankNames.Master,\n        accept: true\n    },\n    {\n        id: \"rank7\",\n        title: \"GrandMaster\",\n        image: \"/images/games/lol/GrandMaster.png\",\n        rank: _constants_lol_constants__WEBPACK_IMPORTED_MODULE_7__.MavelRankNames.GrandMaster,\n        accept: true\n    },\n    {\n        id: \"rank8\",\n        title: \"Celestial\",\n        image: \"/images/games/lol/pbooting.png\",\n        rank: _constants_lol_constants__WEBPACK_IMPORTED_MODULE_7__.MavelRankNames.Celestails,\n        accept: true\n    },\n    {\n        id: \"rank9\",\n        title: \"Eternity\",\n        image: \"/images/games/lol/rw.png\",\n        rank: _constants_lol_constants__WEBPACK_IMPORTED_MODULE_7__.MavelRankNames.Eternity,\n        accept: true\n    }\n];\n// Updated divisions (3, 2, 1 format)\nconst divisions = [\n    \"III\",\n    \"II\",\n    \"I\"\n];\n// Fixed ACC SHARED dropdown options with proper interface\nconst LolRankBoostingDropDown = [\n    {\n        id: \"acc1\",\n        label: \"ACC SHARED\",\n        title: \"ACC SHARED\",\n        value: 0,\n        action: _constants_common__WEBPACK_IMPORTED_MODULE_6__.CurrentLPAction.None\n    },\n    {\n        id: \"acc2\",\n        label: \"PLAY W1 BOOSTERS (100%)\",\n        title: \"PLAY W1 BOOSTERS (100%)\",\n        value: 100,\n        action: _constants_common__WEBPACK_IMPORTED_MODULE_6__.CurrentLPAction.Increase\n    },\n    {\n        id: \"acc3\",\n        label: \"PLAY W1 BOOSTERS (200%)\",\n        title: \"PLAY W1 BOOSTERS (200%)\",\n        value: 200,\n        action: _constants_common__WEBPACK_IMPORTED_MODULE_6__.CurrentLPAction.Increase\n    }\n];\nconst RankBoostingComponent = (param)=>{\n    let { priceControl } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const [discount, setDiscount] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(1);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [currentRank, setCurrentRank] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(updatedRanks[0]);\n    const [acceptableRanks, setAcceptableRanks] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)([]);\n    const [accCurrentDivision, setAccCurrentDivision] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)([]);\n    const [currentDivision, setCurrentDivision] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)();\n    const [currentDivisionInput, setCurrentDivisionInput] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [desiredRank, setDesiredRank] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(updatedRanks[0]);\n    const [desiredDivision, setDesiredDivision] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)();\n    const [accDesiredDivision, setAccDesiredDivision] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)([]);\n    // Price states\n    const [initialPrice, setInitialPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(5.21);\n    const [price, setPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(5.21);\n    const [lPPrice, setLPPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [lPGainPrice, setLPGainPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [pwBoosterPrice, setpwBoosterPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [priorityOrderPrice, setPriorityOrderPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [streamGamesPrice, setStreamGamesPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [soloOnlyPrice, setSoloOnlyPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [expressOrderPrice, setExpressOrderPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [streamingPrice, setStreamingPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [bonusWinPrice, setBonusWinPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [payHalfPrice, setPayHalfPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [normalizeScoresPrice, setNormalizeScoresPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [coachingDuoPrice, setCoachingDuoPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [ghostDuoPrice, setGhostDuoPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    const [accSharedPrice, setAccSharedPrice] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(0);\n    // Boolean states for features\n    const [soloOnly, setSoloOnly] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [offline, setOffline] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [priority, setPriority] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [boosters, setBoosters] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [expressOrder, setExpressOrder] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [streaming, setStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [bonusWin, setBonusWin] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [payHalf, setPayHalf] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [normalizeScores, setNormalizeScores] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [coachingDuo, setCoachingDuo] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [ghostDuo, setGhostDuo] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [bringFriend, setBringFriend] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)();\n    const [queueType, setQueueType] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)();\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(\"4 HOURS 45 MINUTES\");\n    const [hours, setHours] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(4);\n    const [AccShared, setAccShared] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)();\n    const [platformOs, setPlatformOs] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)();\n    const [total, setTotal] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)({\n        price: \"5.21\"\n    });\n    const [priceCt, setPriceCt] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)({});\n    // New state to track if premium options should be enabled\n    const [premiumOptionsEnabled, setPremiumOptionsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_14__.useState)(false);\n    // Check if premium options should be enabled based on AccShared selection\n    const checkPremiumOptionsAvailability = (0,react__WEBPACK_IMPORTED_MODULE_14__.useCallback)((accShared)=>{\n        if (accShared && (accShared.label === \"PLAY W1 BOOSTERS (100%)\" || accShared.label === \"PLAY W1 BOOSTERS (200%)\")) {\n            setPremiumOptionsEnabled(true);\n        } else {\n            setPremiumOptionsEnabled(false);\n            // Reset premium options when they become disabled\n            setCoachingDuo(false);\n            setGhostDuo(false);\n            setBringFriend(false);\n            setCoachingDuoPrice(0);\n            setGhostDuoPrice(0);\n        }\n    }, []);\n    // Updated ACC SHARED handler\n    const handleAccSharedChange = (optn)=>{\n        updateCurrentLP(optn);\n        setAccShared(optn);\n        checkPremiumOptionsAvailability(optn);\n        // Add price adjustment based on selection\n        if (optn.label === \"PLAY W1 BOOSTERS (100%)\") {\n            setAccSharedPrice(5);\n        } else if (optn.label === \"PLAY W1 BOOSTERS (200%)\") {\n            setAccSharedPrice(10);\n        } else {\n            setAccSharedPrice(0);\n        }\n    };\n    // Memoized calculation functions with updated rank structure\n    const calculateNewPrice = (0,react__WEBPACK_IMPORTED_MODULE_14__.useCallback)(()=>{\n        const basePrice = 5.21;\n        const currentRankIndex = updatedRanks.findIndex((rank)=>rank.rank === (currentRank === null || currentRank === void 0 ? void 0 : currentRank.rank));\n        const desiredRankIndex = updatedRanks.findIndex((rank)=>rank.rank === (desiredRank === null || desiredRank === void 0 ? void 0 : desiredRank.rank));\n        const rankDifference = Math.max(0, desiredRankIndex - currentRankIndex);\n        let calculatedPrice = basePrice + rankDifference * 5.21;\n        const divisionDiscount = Math.floor((currentDivisionInput || 0) / 20) * 0.5;\n        calculatedPrice = Math.max(0, calculatedPrice - divisionDiscount);\n        return Number(calculatedPrice.toFixed(2));\n    }, [\n        currentRank,\n        desiredRank,\n        currentDivisionInput\n    ]);\n    const calculateCompletionTime = (0,react__WEBPACK_IMPORTED_MODULE_14__.useCallback)(()=>{\n        const currentRankIndex = updatedRanks.findIndex((rank)=>rank.rank === (currentRank === null || currentRank === void 0 ? void 0 : currentRank.rank));\n        const desiredRankIndex = updatedRanks.findIndex((rank)=>rank.rank === (desiredRank === null || desiredRank === void 0 ? void 0 : desiredRank.rank));\n        const rankDifference = Math.max(0, desiredRankIndex - currentRankIndex);\n        const baseHours = 4;\n        const baseMinutes = 45;\n        const additionalHours = rankDifference * 2;\n        const totalHours = baseHours + additionalHours;\n        setHours(totalHours);\n        return \"\".concat(totalHours, \" HOURS \").concat(baseMinutes, \" MINUTES\");\n    }, [\n        currentRank,\n        desiredRank\n    ]);\n    // Event handlers with updated rank structure\n    const handleCurrentRankChange = (rank)=>{\n        setDesiredDivision(undefined);\n        setCurrentRank(rank);\n        const rankTitles = updatedRanks.map((r)=>r.rank);\n        const currentRankIndex = rankTitles.indexOf(rank.rank);\n        const availableRanks = rankTitles.slice(currentRankIndex);\n        const accRanks = updatedRanks.map((lrank)=>({\n                ...lrank,\n                accept: availableRanks.includes(lrank.rank)\n            }));\n        setAcceptableRanks(accRanks);\n    };\n    const handleCurrentDivisionChange = (division)=>{\n        setCurrentDivision(division);\n        setDesiredDivision(undefined);\n    };\n    const handleDesiredRankChange = (rank)=>{\n        setDesiredRank(rank);\n        setPlatformOs(undefined);\n        setAccShared(undefined);\n        // For highest ranks (Master and above), set to division I\n        if ([\n            \"Master\",\n            \"GrandMaster\",\n            \"Celestial\",\n            \"Eternity\"\n        ].includes(rank === null || rank === void 0 ? void 0 : rank.rank)) {\n            setDesiredDivision({\n                title: \"I\",\n                accept: false\n            });\n        } else {\n            setDesiredDivision(undefined);\n        }\n    };\n    const handleDesiredDivisionChange = (division)=>{\n        setDesiredDivision(division);\n        setAccShared(undefined);\n        setPlatformOs(undefined);\n    };\n    const handleDivisionInputChange = (value)=>{\n        setCurrentDivisionInput(value);\n    };\n    // Price calculation functions - Fixed type casting\n    const updateCurrentLP = (cLP)=>{\n        if (currentDivision && desiredDivision || [\n            \"Master\",\n            \"GrandMaster\",\n            \"Celestial\",\n            \"Eternity\"\n        ].includes(desiredRank === null || desiredRank === void 0 ? void 0 : desiredRank.rank)) {\n            setLPPrice(calculateLP(cLP, initialPrice));\n        }\n    };\n    const calculateLP = (cLP, initial)=>{\n        if (!cLP) return 0;\n        const percentValue = cLP.value / 100 * initial;\n        if ((cLP === null || cLP === void 0 ? void 0 : cLP.action) === _constants_common__WEBPACK_IMPORTED_MODULE_6__.CurrentLPAction.Decrease) {\n            if (cLP.value > 0) {\n                return Number(percentValue.toFixed(2));\n            } else {\n                return 0;\n            }\n        } else if ((cLP === null || cLP === void 0 ? void 0 : cLP.action) === _constants_common__WEBPACK_IMPORTED_MODULE_6__.CurrentLPAction.Increase) {\n            return Number(percentValue.toFixed(2));\n        } else {\n            return 0;\n        }\n    };\n    const updateLPGain = (llpGain)=>{\n        if (currentDivision && desiredDivision || [\n            \"Master\",\n            \"GrandMaster\",\n            \"Celestial\",\n            \"Eternity\"\n        ].includes(desiredRank === null || desiredRank === void 0 ? void 0 : desiredRank.rank)) {\n            setLPGainPrice(calculateLpGain(llpGain, initialPrice));\n        }\n    };\n    const calculateLpGain = (llpGain, initial)=>{\n        if (!llpGain) return 0;\n        const percentValue = llpGain.value / 100 * initial;\n        let finalPrice = 0;\n        if (llpGain && (llpGain === null || llpGain === void 0 ? void 0 : llpGain.action) === _constants_common__WEBPACK_IMPORTED_MODULE_6__.CurrentLPAction.Decrease) {\n            if ((llpGain === null || llpGain === void 0 ? void 0 : llpGain.value) > 0) {\n                finalPrice = Number(percentValue.toFixed(2));\n            } else {\n                finalPrice = 0;\n            }\n        } else if ((llpGain === null || llpGain === void 0 ? void 0 : llpGain.action) === _constants_common__WEBPACK_IMPORTED_MODULE_6__.CurrentLPAction.Increase) {\n            finalPrice = Number(percentValue.toFixed(2));\n        }\n        return finalPrice !== null && finalPrice !== void 0 ? finalPrice : 0;\n    };\n    const handleAddonesChange = (bool, percent, setChange)=>{\n        if (bool) setChange(percent * initialPrice);\n        else setChange(0);\n    };\n    const updateTotalPrice = (0,react__WEBPACK_IMPORTED_MODULE_14__.useCallback)(()=>{\n        const fixedPrice = Number(((price + lPPrice + lPGainPrice + pwBoosterPrice + priorityOrderPrice + streamGamesPrice + soloOnlyPrice + expressOrderPrice + streamingPrice + bonusWinPrice + payHalfPrice + normalizeScoresPrice + coachingDuoPrice + ghostDuoPrice + accSharedPrice) * discount).toFixed(2));\n        if (priceCt && Object.keys(priceCt).length > 0) {\n            try {\n                const result = (0,_utils_now_payments__WEBPACK_IMPORTED_MODULE_10__.calcTotalPricePrecentage)(priceCt, fixedPrice);\n                setTotal(result);\n            } catch (error) {\n                console.error(\"Price calculation error:\", error);\n                setTotal({\n                    price: fixedPrice.toFixed(2)\n                });\n            }\n        } else {\n            setTotal({\n                price: fixedPrice.toFixed(2)\n            });\n        }\n    }, [\n        price,\n        lPPrice,\n        lPGainPrice,\n        pwBoosterPrice,\n        priorityOrderPrice,\n        streamGamesPrice,\n        soloOnlyPrice,\n        expressOrderPrice,\n        streamingPrice,\n        bonusWinPrice,\n        payHalfPrice,\n        normalizeScoresPrice,\n        coachingDuoPrice,\n        ghostDuoPrice,\n        accSharedPrice,\n        discount,\n        priceCt\n    ]);\n    const getPriceControl = (0,react__WEBPACK_IMPORTED_MODULE_14__.useCallback)(async ()=>{\n        try {\n            setPriceCt(priceControl || {});\n        } catch (error) {\n            console.error(\"Price control error:\", error);\n            setPriceCt({});\n        }\n    }, [\n        priceControl\n    ]);\n    // Submit handler\n    const handleSubmit = async ()=>{\n        if (!currentRank || !currentRank.rank) return (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_11__.errorToast)(\"Please select your current Rank!\");\n        if (!desiredRank || !desiredRank.rank) return (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_11__.errorToast)(\"Please select your desired Rank!\");\n        if (!currentDivision || !currentDivision.title) return (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_11__.errorToast)(\"Please select your current Division!\");\n        if (!desiredDivision || !desiredDivision.title) return (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_11__.errorToast)(\"Please select your desired Division!\");\n        if (!role) return (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_11__.errorToast)(\"Please select your role!\");\n        setLoading(true);\n        try {\n            const orderId = await (0,_database_functions_database__WEBPACK_IMPORTED_MODULE_9__.createOrder)({\n                game_name: _constants_common__WEBPACK_IMPORTED_MODULE_6__.GameNames.MarvelRivals,\n                service_type: _constants_service_types__WEBPACK_IMPORTED_MODULE_8__.LolServiceTypes.RankBoosting,\n                order_details: JSON.stringify({\n                    platform: platformOs,\n                    currentLevel: currentRank,\n                    desiredLevel: desiredRank,\n                    currentDivision,\n                    currentDivisionInput,\n                    desiredDivision,\n                    AccShared,\n                    role,\n                    queueType\n                }),\n                play_with_boosters: boosters,\n                appear_offline: offline,\n                champions_roles: roles,\n                priority_order: priority,\n                stream_game: stream,\n                completion_time_hours: hours,\n                price: parseFloat(total === null || total === void 0 ? void 0 : total.price) || price\n            });\n            if (orderId) {\n                router.push(\"/checkout?order_id=\".concat(orderId));\n            } else {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_11__.errorToast)(\"Failed to create order. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Order creation failed:\", error);\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_11__.errorToast)(\"Failed to create order. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_14__.useEffect)(()=>{\n        const newPrice = calculateNewPrice();\n        setPrice(newPrice);\n        setInitialPrice(newPrice);\n        const newDuration = calculateCompletionTime();\n        setDuration(newDuration);\n    }, [\n        calculateNewPrice,\n        calculateCompletionTime\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_14__.useEffect)(()=>{\n        updateTotalPrice();\n    }, [\n        updateTotalPrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_14__.useEffect)(()=>{\n        if (!desiredRank || !currentDivision) return;\n        // All ranks including Master+ have divisions III, II, I\n        const currentDivIndex = divisions.findIndex((divn)=>divn === currentDivision.title);\n        if ((desiredRank === null || desiredRank === void 0 ? void 0 : desiredRank.rank) === (currentRank === null || currentRank === void 0 ? void 0 : currentRank.rank)) {\n            // Same rank: only allow higher divisions (lower numbers)\n            const availableDivisions = currentDivIndex >= 0 ? divisions.slice(0, currentDivIndex) : [];\n            setAccDesiredDivision(divisions.map((divn)=>({\n                    title: divn,\n                    accept: availableDivisions.includes(divn)\n                })));\n        } else {\n            // Different rank: all divisions available\n            setAccDesiredDivision(divisions.map((divn)=>({\n                    title: divn,\n                    accept: true\n                })));\n        }\n        setAccShared(undefined);\n        setPlatformOs(undefined);\n    }, [\n        currentDivision,\n        currentRank,\n        desiredRank\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_14__.useEffect)(()=>{\n        const divns = divisions.map((divn)=>({\n                title: divn,\n                accept: true\n            }));\n        const accRanks = updatedRanks.map((lrank)=>({\n                ...lrank,\n                accept: true\n            }));\n        setAcceptableRanks(accRanks);\n        setAccDesiredDivision(divns);\n        setAccCurrentDivision(divns);\n        setDesiredDivision(divns[1]); // Set to \"II\"\n        setCurrentDivision(divns[0]); // Set to \"III\"\n        setCurrentRank(updatedRanks[0]); // Set to Bronze\n        setDesiredRank(updatedRanks[0]); // Set to Bronze\n        getPriceControl();\n    }, [\n        getPriceControl\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_14__.useEffect)(()=>{\n        handleAddonesChange(boosters, 0.4, setpwBoosterPrice);\n        handleAddonesChange(priority, 0.2, setPriorityOrderPrice);\n        handleAddonesChange(stream, 0.1, setStreamGamesPrice);\n        handleAddonesChange(soloOnly, 0.6, setSoloOnlyPrice);\n        handleAddonesChange(expressOrder, 0.3, setExpressOrderPrice);\n        handleAddonesChange(streaming, 0.15, setStreamingPrice);\n        handleAddonesChange(bonusWin, 0.0, setBonusWinPrice);\n        handleAddonesChange(normalizeScores, 0.6, setNormalizeScoresPrice);\n        handleAddonesChange(coachingDuo, 0.5, setCoachingDuoPrice);\n        handleAddonesChange(ghostDuo, 0.6, setGhostDuoPrice);\n    }, [\n        price,\n        initialPrice,\n        boosters,\n        stream,\n        priority,\n        soloOnly,\n        expressOrder,\n        streaming,\n        bonusWin,\n        normalizeScores,\n        coachingDuo,\n        ghostDuo\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"col-span-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-12 gap-3 p-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"col-span-12 md:col-span-9 bg-primary-dark rounded-md px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-12 md:col-span-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 justify-start items-center h-[100px]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: (currentRank === null || currentRank === void 0 ? void 0 : currentRank.image) || \"/images/games/lol/Bronze.png\",\n                                                alt: \"current rank\",\n                                                height: 105,\n                                                width: 105\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"font-montserrat text-lg font-semibold\",\n                                                        children: \"Current Rank\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-montserrat text-primary-grey-text text-sm\",\n                                                        children: (currentRank === null || currentRank === void 0 ? void 0 : currentRank.title) || \"Bronze\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ranks_grid__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        onSelect: handleCurrentRankChange,\n                                        ranks: updatedRanks,\n                                        type: \"current\",\n                                        defaultSelected: currentRank\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-montserrat text-md font-medium mb-3\",\n                                                children: \"Current Division\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_division_grid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                divisions: accCurrentDivision,\n                                                onSelect: handleCurrentDivisionChange\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-montserrat text-md font-medium mb-3\",\n                                                children: \"Current Division Value\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"0\",\n                                                value: currentDivisionInput,\n                                                onChange: (e)=>handleDivisionInputChange(Number(e.target.value) || 0),\n                                                className: \"w-full px-3 py-2 bg-[#1C1E2B] border border-secondary-dark rounded-md text-white font-montserrat\",\n                                                placeholder: \"Enter division value (e.g., 40 for -$1.0 discount)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            title: \"ACC SHARED OR PLAY W BOOSTERS\",\n                                            options: LolRankBoostingDropDown,\n                                            placeholder: \"ACC SHARED\",\n                                            selectedValue: AccShared,\n                                            onSelect: handleAccSharedChange\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-12 mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start gap-2 col-span-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    title: \"Select Platform\",\n                                                    options: _constants_lol_constants__WEBPACK_IMPORTED_MODULE_7__.lolplatformOsDropDwon || [],\n                                                    selectedValue: platformOs,\n                                                    placeholder: \"Platform\",\n                                                    onSelect: (optn)=>{\n                                                        updateLPGain(optn);\n                                                        setPlatformOs(optn);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    title: \"Select Role\",\n                                                    options: _constants_lol_constants__WEBPACK_IMPORTED_MODULE_7__.LolRoleDropDown || [],\n                                                    placeholder: \"Role\",\n                                                    selectedValue: role,\n                                                    onSelect: setRole\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-12 md:col-span-6 mt-8 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 justify-start items-center h-[100px]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: (desiredRank === null || desiredRank === void 0 ? void 0 : desiredRank.image) || \"/images/games/lol/Bronze.png\",\n                                                alt: \"desired rank\",\n                                                height: 105,\n                                                width: 105\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"font-montserrat text-lg font-semibold\",\n                                                        children: \"Desired Rank\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-montserrat text-primary-grey-text text-sm\",\n                                                        children: (desiredRank === null || desiredRank === void 0 ? void 0 : desiredRank.title) || \"Bronze\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ranks_grid__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        onSelect: handleDesiredRankChange,\n                                        ranks: acceptableRanks,\n                                        type: \"desired\",\n                                        defaultSelected: desiredRank\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-montserrat text-md font-medium mb-3\",\n                                                children: \"Desired Division\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_division_grid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                divisions: accDesiredDivision,\n                                                onSelect: handleDesiredDivisionChange\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                    lineNumber: 452,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"col-span-12 md:col-span-3 bg-primary-dark rounded-md p-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1C1E2B] flex justify-between items-center p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start items-center h-[80px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: (currentRank === null || currentRank === void 0 ? void 0 : currentRank.image) || \"/images/games/lol/Bronze.png\",\n                                                height: 85,\n                                                width: 85,\n                                                alt: \"rank-boost\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-center\",\n                                            children: (currentRank === null || currentRank === void 0 ? void 0 : currentRank.title) || \"Bronze\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-fit bg-primary-purple flex justify-center items-center p-2 rounded-3xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                            className: \"image\",\n                                            src: \"/images/icons/arrow-right-short.svg\",\n                                            alt: \"hero-image\",\n                                            height: 22,\n                                            width: 22\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start items-center h-[80px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: (desiredRank === null || desiredRank === void 0 ? void 0 : desiredRank.image) || \"/images/games/lol/Bronze.png\",\n                                                height: 85,\n                                                width: 85,\n                                                alt: \"rank-boost\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-center\",\n                                            children: (desiredRank === null || desiredRank === void 0 ? void 0 : desiredRank.title) || \"Bronze\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-[#1C1E2B] p-3 mt-2 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-montserrat text-sm font-medium\",\n                                            children: \"COMPLETION TIME\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-montserrat text-sm font-semibold\",\n                                            children: duration\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-montserrat text-sm font-medium\",\n                                            children: \"ACTIVE BOOSTERS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-montserrat text-sm font-semibold\",\n                                                    children: \"61\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center my-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ms-3 text-sm font-montserrat text-primary-grey-text\",\n                                    children: \"Play with Boosters (+40%)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            value: \"\".concat(boosters),\n                                            onChange: (e)=>{\n                                                const percentValue = 0.4 * total;\n                                                if (e.target.checked) {\n                                                    setpwBoosterPrice(Number(percentValue.toFixed(2)));\n                                                } else {\n                                                    setpwBoosterPrice(0);\n                                                }\n                                                setBoosters((prev)=>!prev);\n                                            },\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-primary-dark-grey peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-[#fff] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-purple\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 18\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center my-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ms-3 text-sm font-montserrat text-primary-grey-text\",\n                                    children: \"Appear Offline (Free)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            value: \"\".concat(offline),\n                                            onChange: ()=>setOffline((prev)=>!prev),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-primary-dark-grey peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-[#fff] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-purple\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center my-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ms-3 text-sm font-montserrat text-primary-grey-text\",\n                                    children: \"Champions/Roles (Free)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            value: \"\".concat(roles),\n                                            onChange: ()=>setRoles((prev)=>!prev),\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-primary-dark-grey peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-[#fff] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-purple\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center my-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ms-3 text-sm font-montserrat text-primary-grey-text\",\n                                    children: \"Priority Order (+20%)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            value: \"\".concat(priority),\n                                            onChange: (e)=>{\n                                                const percentValue = 20 / 100 * total;\n                                                if (e.target.checked) {\n                                                    setPriorityOrderPrice(Number(percentValue.toFixed(2)));\n                                                } else {\n                                                    setPriorityOrderPrice(0);\n                                                }\n                                                setPriority((prev)=>!prev);\n                                            },\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-primary-dark-grey peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-[#fff] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-purple\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center my-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ms-3 text-sm font-montserrat text-primary-grey-text\",\n                                    children: \"Stream Games (+10%)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            value: \"\".concat(stream),\n                                            onChange: (e)=>{\n                                                const percentValue = 10 / 100 * total;\n                                                if (e.target.checked) {\n                                                    setStreamGamesPrice(Number(percentValue.toFixed(2)));\n                                                } else {\n                                                    setStreamGamesPrice(0);\n                                                }\n                                                setStream((prev)=>!prev);\n                                            },\n                                            className: \"sr-only peer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-11 h-6 bg-primary-dark-grey peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-[#fff] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-purple\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                            lineNumber: 728,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_discountCode__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            setDiscount: setDiscount\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                            lineNumber: 748,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-montserrat font-semibold\",\n                                    children: \"Total Amount\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-montserrat font-semibold\",\n                                    children: [\n                                        \"$\",\n                                        (total === null || total === void 0 ? void 0 : total.price) || (price || 0).toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                            lineNumber: 751,\n                            columnNumber: 11\n                        }, undefined),\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            disabled: true,\n                            type: \"button\",\n                            className: \"flex justify-center items-center text-white bg-secondary-600 font-medium w-full rounded-lg text-sm me-2 mb-2 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_15__.Bars, {\n                                height: \"40\",\n                                color: \"white\",\n                                ariaLabel: \"bars-loading\",\n                                wrapperStyle: {},\n                                wrapperClass: \"\",\n                                visible: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                            lineNumber: 759,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: handleSubmit,\n                            className: \"text-white bg-primary-purple hover:bg-primary-shaded-purple focus:ring-4 w-full focus:ring-blue-300 font-medium rounded-lg text-sm px-7 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\",\n                            children: \"Rank Up Now\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ms-3 text-xs font-montserrat text-primary-grey-text text-center\",\n                            children: [\n                                \"Completion Time: \",\n                                duration\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                            lineNumber: 783,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n                    lineNumber: 590,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n            lineNumber: 451,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\rank-boosting.tsx\",\n        lineNumber: 450,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RankBoostingComponent, \"OQOamXTq4q7VdgQofcV8RqQwGHg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter\n    ];\n});\n_c = RankBoostingComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RankBoostingComponent);\nvar _c;\n$RefreshReg$(_c, \"RankBoostingComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/marvel-rivals/[...slug]/components/rank-boosting.tsx\n"));

/***/ })

});