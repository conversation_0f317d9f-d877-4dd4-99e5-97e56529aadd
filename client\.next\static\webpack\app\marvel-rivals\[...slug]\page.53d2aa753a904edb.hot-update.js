"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/marvel-rivals/[...slug]/page",{

/***/ "(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx":
/*!*******************************************************************!*\
  !*** ./app/marvel-rivals/[...slug]/components/level-boosting.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_custom_dropdown__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/custom-dropdown */ \"(app-pages-browser)/./components/custom-dropdown.tsx\");\n/* harmony import */ var _components_discountCode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/discountCode */ \"(app-pages-browser)/./components/discountCode.tsx\");\n/* harmony import */ var _constants_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constants/common */ \"(app-pages-browser)/./constants/common.ts\");\n/* harmony import */ var _constants_service_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants/service-types */ \"(app-pages-browser)/./constants/service-types.ts\");\n/* harmony import */ var _database_functions_database__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/database/functions.database */ \"(app-pages-browser)/./database/functions.database.ts\");\n/* harmony import */ var _utils_now_payments__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/now-payments */ \"(app-pages-browser)/./utils/now-payments.ts\");\n/* harmony import */ var _utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/toast-utils */ \"(app-pages-browser)/./utils/toast-utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-loader-spinner */ \"(app-pages-browser)/./node_modules/react-loader-spinner/dist/module.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst MultiRangeSlider = (param)=>{\n    let { min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange } = param;\n    _s();\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0);\n    // Level markers for the slider\n    const levelMarkers = [\n        1,\n        10,\n        20,\n        30,\n        40,\n        50,\n        60,\n        70,\n        80,\n        90,\n        100\n    ];\n    // Calculate percentage position for any level value\n    const getPercent = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)((value)=>{\n        // Ensure value is within bounds\n        const clampedValue = Math.max(min, Math.min(max, value));\n        return (clampedValue - min) / (max - min) * 100;\n    }, [\n        min,\n        max\n    ]);\n    // Get level value from percentage position\n    const getValueFromPercent = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)((percent)=>{\n        const clampedPercent = Math.max(0, Math.min(100, percent));\n        const rawValue = min + clampedPercent / 100 * (max - min);\n        return Math.round(rawValue);\n    }, [\n        min,\n        max\n    ]);\n    // Find the closest level marker to a given value for snapping\n    const snapToNearestMarker = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)((value)=>{\n        let closest = value;\n        let minDistance = Infinity;\n        levelMarkers.forEach((marker)=>{\n            const distance = Math.abs(value - marker);\n            if (distance < minDistance) {\n                minDistance = distance;\n                closest = marker;\n            }\n        });\n        // Only snap if within 3 levels of a marker\n        return minDistance <= 3 ? closest : value;\n    }, [\n        levelMarkers\n    ]);\n    const handleMouseDown = (type)=>(event)=>{\n            if (!sliderRef.current) return;\n            setIsDragging(type);\n            event.preventDefault();\n            event.stopPropagation();\n        };\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)((event)=>{\n        if (!isDragging || !sliderRef.current) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = Math.min(100, Math.max(0, (event.clientX - rect.left) / rect.width * 100 - dragOffset));\n        const value = getValueFromPercent(percent);\n        if (isDragging === \"current\") {\n            const newCurrent = Math.min(Math.max(min, value), desiredLevel - 1);\n            onCurrentChange(newCurrent);\n        } else if (isDragging === \"desired\") {\n            const newDesired = Math.max(Math.min(max, value), currentLevel + 1);\n            onDesiredChange(newDesired);\n        }\n    }, [\n        isDragging,\n        min,\n        max,\n        currentLevel,\n        desiredLevel,\n        onCurrentChange,\n        onDesiredChange,\n        dragOffset,\n        getValueFromPercent\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(()=>{\n        setIsDragging(null);\n        setDragOffset(0);\n    }, []);\n    const handleTrackClick = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)((event)=>{\n        if (!sliderRef.current || isDragging) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = (event.clientX - rect.left) / rect.width * 100;\n        const value = getValueFromPercent(percent);\n        const currentPercent = getPercent(currentLevel);\n        const desiredPercent = getPercent(desiredLevel);\n        const clickPercent = percent;\n        // Determine which handle is closer to the click\n        const distanceToCurrent = Math.abs(clickPercent - currentPercent);\n        const distanceToDesired = Math.abs(clickPercent - desiredPercent);\n        if (distanceToCurrent < distanceToDesired) {\n            // Move current level handle\n            const newCurrent = Math.min(Math.max(min, value), desiredLevel - 1);\n            onCurrentChange(newCurrent);\n        } else {\n            // Move desired level handle\n            const newDesired = Math.max(Math.min(max, value), currentLevel + 1);\n            onDesiredChange(newDesired);\n        }\n    }, [\n        currentLevel,\n        desiredLevel,\n        min,\n        max,\n        onCurrentChange,\n        onDesiredChange,\n        getPercent,\n        getValueFromPercent,\n        isDragging\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    const currentPercent = getPercent(currentLevel);\n    const desiredPercent = getPercent(desiredLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-8 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-6\",\n                ref: sliderRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-8 bg-secondary-dark rounded-lg border border-secondary-dark cursor-pointer\",\n                    onClick: handleTrackClick,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute h-8 bg-gradient-to-r from-primary-yellow to-primary-orange rounded-lg transition-all duration-200 shadow-inner\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                width: \"\".concat(Math.max(0, desiredPercent - currentPercent), \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-8 h-8 bg-primary-yellow rounded-full border-3 border-white shadow-lg transition-all duration-200 cursor-grab z-30 flex items-center justify-center \".concat(isDragging === \"current\" ? \"cursor-grabbing scale-110\" : \"hover:scale-105\"),\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"current\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-black rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-primary-yellow text-black px-3 py-1 rounded-lg text-xs font-bold whitespace-nowrap font-montserrat shadow-lg\",\n                                    children: [\n                                        \"Current: \",\n                                        currentLevel\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-8 h-8 bg-primary-orange rounded-full border-3 border-white shadow-lg transition-all duration-200 cursor-grab z-30 flex items-center justify-center \".concat(isDragging === \"desired\" ? \"cursor-grabbing scale-110\" : \"hover:scale-105\"),\n                            style: {\n                                left: \"\".concat(desiredPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"desired\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-white rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-primary-orange text-white px-3 py-1 rounded-lg text-xs font-bold whitespace-nowrap font-montserrat shadow-lg\",\n                                    children: [\n                                        \"Target: \",\n                                        desiredLevel\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-4 bg-red-text\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between text-sm text-gray-400 font-montserrat\",\n                    children: levelMarkers.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold text-white \".concat(level === 1 ? \"text-start\" : level === 100 ? \"text-end\" : \"text-center\", \" \"),\n                                    children: level\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, undefined),\n                                level === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"START\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 31\n                                }, undefined),\n                                level === 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: \"MAX\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, level, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-primary-dark-grey rounded-lg border border-secondary-dark\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-yellow font-bold text-lg font-montserrat\",\n                                        children: currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"CURRENT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-purple font-bold text-lg font-montserrat\",\n                                        children: desiredLevel - currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"LEVELS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-orange font-bold text-lg font-montserrat\",\n                                        children: desiredLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"TARGET\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-400 font-bold font-montserrat\",\n                            children: [\n                                currentLevel,\n                                \" → \",\n                                desiredLevel,\n                                \" (\",\n                                desiredLevel - currentLevel,\n                                \" levels)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MultiRangeSlider, \"DZrzqvATeqtwuIc2mm3rPJitWvM=\");\n_c = MultiRangeSlider;\nconst LevelBoostingComponent = (param)=>{\n    let { priceControl } = param;\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [discount, setDiscount] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(1);\n    // Level states\n    const [currentLevel, setCurrentLevel] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(21);\n    const [desiredLevel, setDesiredLevel] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(64);\n    // Mock data\n    const LolLevelBoostingDropDown = [\n        {\n            id: \"acc1\",\n            label: \"ACC SHARED\",\n            title: \"ACC SHARED\",\n            value: 0,\n            action: \"None\"\n        },\n        {\n            id: \"acc2\",\n            label: \"PLAY W BOOSTERS (100%)\",\n            title: \"PLAY W BOOSTERS (100%)\",\n            value: 100,\n            action: \"Increase\"\n        },\n        {\n            id: \"acc3\",\n            label: \"PLAY W BOOSTERS (200%)\",\n            title: \"PLAY W BOOSTERS (200%)\",\n            value: 200,\n            action: \"Increase\"\n        }\n    ];\n    const platformOptions = [\n        {\n            id: \"pc\",\n            label: \"PC\",\n            title: \"PC\"\n        },\n        {\n            id: \"console\",\n            label: \"CONSOLE\",\n            title: \"CONSOLE\"\n        }\n    ];\n    const roleOptions = [\n        {\n            id: \"any\",\n            label: \"ANY ROLE\",\n            title: \"ANY ROLE\"\n        },\n        {\n            id: \"tank\",\n            label: \"TANK\",\n            title: \"TANK\"\n        },\n        {\n            id: \"damage\",\n            label: \"DAMAGE\",\n            title: \"DAMAGE\"\n        },\n        {\n            id: \"support\",\n            label: \"SUPPORT\",\n            title: \"SUPPORT\"\n        }\n    ];\n    // Selection states\n    const [accShared, setAccShared] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [platform, setPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    // Price states\n    const [basePrice, setBasePrice] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0);\n    const [accSharedPrice, setAccSharedPrice] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0);\n    const [boostersPrice, setBoostersPrice] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0);\n    const [priorityPrice, setPriorityPrice] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0);\n    const [streamPrice, setStreamPrice] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0);\n    const [completionTime, setCompletionTime] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        days: 0,\n        hours: 0\n    });\n    const [total, setTotal] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        price: \"5.21\"\n    });\n    const [priceCt, setPriceCt] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({});\n    // Add-on states\n    const [boosters, setBoosters] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [offline, setOffline] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [priority, setPriority] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    // Level adjustment functions\n    const adjustCurrentLevel = (increment)=>{\n        setCurrentLevel((prev)=>{\n            const newLevel = Math.max(1, Math.min(99, prev + increment));\n            if (newLevel >= desiredLevel) {\n                setDesiredLevel(Math.min(100, newLevel + 1));\n            }\n            return newLevel;\n        });\n    };\n    const adjustDesiredLevel = (increment)=>{\n        setDesiredLevel((prev)=>{\n            const newLevel = Math.max(currentLevel + 1, Math.min(100, prev + increment));\n            return newLevel;\n        });\n    };\n    // Calculate price based on level difference\n    const calculateBasePrice = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return 5.21;\n        }\n        const maxPrice = 2681.80;\n        const minPrice = 5.21;\n        const priceRange = maxPrice - minPrice;\n        const calculatedPrice = minPrice + (levelDifference - 1) / 98 * priceRange;\n        return Math.max(minPrice, calculatedPrice);\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Calculate completion time\n    const calculateCompletionTime = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return {\n                days: 0,\n                hours: 1\n            };\n        }\n        const maxDays = 73;\n        const maxHours = 5;\n        const totalMaxHours = maxDays * 24 + maxHours;\n        const totalHours = levelDifference / 99 * totalMaxHours;\n        const days = Math.floor(totalHours / 24);\n        const hours = Math.round(totalHours % 24);\n        return {\n            days,\n            hours\n        };\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Handle ACC SHARED selection\n    const handleAccSharedChange = (option)=>{\n        setAccShared(option);\n        if (option.label === \"PLAY W BOOSTERS (100%)\") {\n            setAccSharedPrice(basePrice * 1.0);\n        } else if (option.label === \"PLAY W BOOSTERS (200%)\") {\n            setAccSharedPrice(basePrice * 2.0);\n        } else {\n            setAccSharedPrice(0);\n        }\n    };\n    // Handle addon changes with proper price calculation\n    const handleAddonChange = (isEnabled, percentage, setPriceFunction)=>{\n        if (isEnabled) {\n            setPriceFunction(basePrice * percentage);\n        } else {\n            setPriceFunction(0);\n        }\n    };\n    // Calculate total price\n    const updateTotalPrice = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(()=>{\n        const subtotal = basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice;\n        const finalPrice = subtotal * discount;\n        if (priceCt && Object.keys(priceCt).length > 0) {\n            try {\n                const result = (0,_utils_now_payments__WEBPACK_IMPORTED_MODULE_6__.calcTotalPricePrecentage)(priceCt, finalPrice);\n                setTotal(result);\n            } catch (error) {\n                console.error(\"Price calculation error:\", error);\n                setTotal({\n                    price: finalPrice.toFixed(2)\n                });\n            }\n        } else {\n            setTotal({\n                price: finalPrice.toFixed(2)\n            });\n        }\n    }, [\n        basePrice,\n        accSharedPrice,\n        boostersPrice,\n        priorityPrice,\n        streamPrice,\n        discount,\n        priceCt\n    ]);\n    // Handle order creation with proper validation and error handling\n    const handleSubmit = async ()=>{\n        // Validation\n        if (desiredLevel <= currentLevel) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.errorToast)(\"Desired level must be higher than current level!\");\n            return;\n        }\n        if (!accShared) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.errorToast)(\"Please select account sharing option!\");\n            return;\n        }\n        if (!platform) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.errorToast)(\"Please select a platform!\");\n            return;\n        }\n        if (!role) {\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.errorToast)(\"Please select a role!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const orderId = await (0,_database_functions_database__WEBPACK_IMPORTED_MODULE_5__.createOrder)({\n                game_name: _constants_common__WEBPACK_IMPORTED_MODULE_3__.GameNames.LeagueOfLegends,\n                service_type: _constants_service_types__WEBPACK_IMPORTED_MODULE_4__.MarvelRivalsServiceTypes.LevelBoosting,\n                order_details: JSON.stringify({\n                    currentLevel,\n                    desiredLevel,\n                    levelDifference: desiredLevel - currentLevel,\n                    platform: platform.label,\n                    role: role.label,\n                    accShared: accShared.label,\n                    addons: {\n                        boosters,\n                        offline,\n                        roles,\n                        priority,\n                        stream\n                    }\n                }),\n                play_with_boosters: boosters,\n                appear_offline: offline,\n                champions_roles: roles,\n                priority_order: priority,\n                stream_game: stream,\n                completion_time_hours: completionTime.days * 24 + completionTime.hours,\n                price: parseFloat(total === null || total === void 0 ? void 0 : total.price) || basePrice\n            });\n            if (orderId) {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.successToast)(\"Order created successfully!\");\n                router.push(\"/checkout?order_id=\".concat(orderId));\n            } else {\n                (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.errorToast)(\"Failed to create order. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Order creation failed:\", error);\n            (0,_utils_toast_utils__WEBPACK_IMPORTED_MODULE_7__.errorToast)(\"Failed to create order. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Get price control\n    const getPriceControl = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(async ()=>{\n        try {\n            setPriceCt(priceControl || {});\n        } catch (error) {\n            console.error(\"Price control error:\", error);\n            setPriceCt({});\n        }\n    }, [\n        priceControl\n    ]);\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        const newPrice = calculateBasePrice();\n        setBasePrice(newPrice);\n        const newTime = calculateCompletionTime();\n        setCompletionTime(newTime);\n    }, [\n        calculateBasePrice,\n        calculateCompletionTime\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        updateTotalPrice();\n    }, [\n        updateTotalPrice\n    ]);\n    // Update prices when base price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        if (accShared) {\n            handleAccSharedChange(accShared);\n        }\n    }, [\n        basePrice\n    ]);\n    // Handle addon price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        handleAddonChange(boosters, 0.4, setBoostersPrice);\n    }, [\n        boosters,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        handleAddonChange(priority, 0.2, setPriorityPrice);\n    }, [\n        priority,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        handleAddonChange(stream, 0.1, setStreamPrice);\n    }, [\n        stream,\n        basePrice\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        getPriceControl();\n    }, [\n        getPriceControl\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"col-span-12 \",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-12 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-9 bg-primary-dark rounded-md px-6 py-8 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center text-xl font-bold mr-4 text-white\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white font-montserrat\",\n                                                children: \"SELECT YOUR CURRENT AND DESIRED LEVEL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-4xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"CURRENT LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-yellow font-montserrat\",\n                                                                    children: currentLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel <= 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 538,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-yellow text-black px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: currentLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 540,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel >= 99,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 548,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 543,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"DESIRED LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-orange font-montserrat\",\n                                                                    children: desiredLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel <= currentLevel + 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 563,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-orange text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: desiredLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 565,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel >= 100,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 573,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiRangeSlider, {\n                                                    min: 1,\n                                                    max: 100,\n                                                    currentLevel: currentLevel,\n                                                    desiredLevel: desiredLevel,\n                                                    onCurrentChange: setCurrentLevel,\n                                                    onDesiredChange: setDesiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid items-center grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"ACC SHARED OR PLAY\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"W BOOSTERS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                options: LolLevelBoostingDropDown,\n                                                selectedValue: accShared,\n                                                onSelect: handleAccSharedChange,\n                                                placeholder: \"ACC SHARED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT ROLE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                options: roleOptions,\n                                                selectedValue: role,\n                                                onSelect: setRole,\n                                                placeholder: \"ANY ROLE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT PLATFORM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_dropdown__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                options: platformOptions,\n                                                selectedValue: platform,\n                                                onSelect: setPlatform,\n                                                placeholder: \"PC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-3 bg-primary-dark rounded-md p-2 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-secondary-dark p-4 rounded-lg mb-6 flex items-center justify-center border border-secondary-dark\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400 mb-2 font-montserrat\",\n                                            children: \"BOOST FROM LEVEL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold mb-3 font-montserrat\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-yellow\",\n                                                    children: currentLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 mx-2\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-orange\",\n                                                    children: desiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-primary-yellow to-primary-orange text-white px-4 py-2 rounded-full text-sm font-bold font-montserrat\",\n                                            children: [\n                                                \"+\",\n                                                desiredLevel - currentLevel,\n                                                \" LEVELS\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Play with Boosters (+40%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: boosters,\n                                                        onChange: ()=>setBoosters(!boosters),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Appear Offline (Free)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: offline,\n                                                        onChange: ()=>setOffline(!offline),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Champions/Roles (Free)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: roles,\n                                                        onChange: ()=>setRoles(!roles),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Priority Order (+20%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: priority,\n                                                        onChange: ()=>setPriority(!priority),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: \"Stream Games (+10%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: stream,\n                                                        onChange: ()=>setStream(!stream),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_discountCode__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                setDiscount: setDiscount,\n                                discount: discount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 13\n                            }, undefined),\n                            discount < 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-medium text-gray-400\",\n                                                children: \"Original Price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-medium text-gray-400 line-through\",\n                                                children: [\n                                                    \"$\",\n                                                    (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 736,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"rounded-sm border border-green-500 p-3 flex justify-between items-center bg-[#1C1E2B]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-semibold text-green-400\",\n                                                children: [\n                                                    \"Total Amount (\",\n                                                    Math.round((1 - discount) * 100),\n                                                    \"% OFF)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-montserrat font-semibold text-green-400\",\n                                                children: [\n                                                    \"$\",\n                                                    (total === null || total === void 0 ? void 0 : total.price) || ((basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice) * discount).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B] mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-montserrat font-semibold\",\n                                        children: \"Total Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-montserrat font-semibold\",\n                                        children: [\n                                            \"$\",\n                                            (total === null || total === void 0 ? void 0 : total.price) || (basePrice + accSharedPrice + boostersPrice + priorityPrice + streamPrice).toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 752,\n                                columnNumber: 15\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                disabled: true,\n                                type: \"button\",\n                                className: \"flex justify-center items-center text-white bg-secondary-600 font-medium w-full rounded-lg text-sm me-2 mb-2 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_10__.Bars, {\n                                    height: \"40\",\n                                    color: \"white\",\n                                    ariaLabel: \"bars-loading\",\n                                    wrapperStyle: {},\n                                    wrapperClass: \"\",\n                                    visible: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 767,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 762,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleSubmit,\n                                className: \"text-white bg-primary-purple hover:bg-primary-shaded-purple focus:ring-4 w-full focus:ring-blue-300 font-medium rounded-lg text-sm px-7 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\",\n                                children: \"Level Up Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 777,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-xs text-gray-400 mt-4 font-montserrat\",\n                                children: [\n                                    \"Completion Time: \",\n                                    completionTime.days,\n                                    \" Days \",\n                                    completionTime.hours,\n                                    \" Hours\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 787,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 512,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n            lineNumber: 511,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 510,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(LevelBoostingComponent, \"6QjUyUuzW16cIsH+qP6AcXp4LIs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter\n    ];\n});\n_c1 = LevelBoostingComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LevelBoostingComponent);\nvar _c, _c1;\n$RefreshReg$(_c, \"MultiRangeSlider\");\n$RefreshReg$(_c1, \"LevelBoostingComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx\n"));

/***/ })

});