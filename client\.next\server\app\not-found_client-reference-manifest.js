globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/not-found"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/providers.tsx":{"*":{"id":"(ssr)/./app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/footer/index.tsx":{"*":{"id":"(ssr)/./components/footer/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/nav/index.tsx":{"*":{"id":"(ssr)/./components/nav/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs":{"*":{"id":"(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":{"*":{"id":"(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs":{"*":{"id":"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/home/<USER>/Blogs.tsx":{"*":{"id":"(ssr)/./app/home/<USER>/Blogs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/home/<USER>/DiscountBanner.tsx":{"*":{"id":"(ssr)/./app/home/<USER>/DiscountBanner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/home/<USER>/FAQ.tsx":{"*":{"id":"(ssr)/./app/home/<USER>/FAQ.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/home/<USER>/games-grid.tsx":{"*":{"id":"(ssr)/./app/home/<USER>/games-grid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/home/<USER>/ProcessFlow.tsx":{"*":{"id":"(ssr)/./app/home/<USER>/ProcessFlow.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/home/<USER>/ProgressJumbotron.tsx":{"*":{"id":"(ssr)/./app/home/<USER>/ProgressJumbotron.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/home/<USER>/Reviews.tsx":{"*":{"id":"(ssr)/./app/home/<USER>/Reviews.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/is-user-login.tsx":{"*":{"id":"(ssr)/./components/is-user-login.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\providers.tsx":{"id":"(app-pages-browser)/./app/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\components\\footer\\index.tsx":{"id":"(app-pages-browser)/./components/footer/index.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\components\\nav\\index.tsx":{"id":"(app-pages-browser)/./components/nav/index.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\@vercel\\analytics\\dist\\react\\index.mjs":{"id":"(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\@vercel\\speed-insights\\dist\\next\\index.mjs":{"id":"(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/next/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\react-toastify\\dist\\react-toastify.esm.mjs":{"id":"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\react-toastify\\dist\\ReactToastify.css":{"id":"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\home\\components\\Blogs.tsx":{"id":"(app-pages-browser)/./app/home/<USER>/Blogs.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\home\\components\\DiscountBanner.tsx":{"id":"(app-pages-browser)/./app/home/<USER>/DiscountBanner.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\home\\components\\FAQ.tsx":{"id":"(app-pages-browser)/./app/home/<USER>/FAQ.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\home\\components\\games-grid.tsx":{"id":"(app-pages-browser)/./app/home/<USER>/games-grid.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\home\\components\\ProcessFlow.tsx":{"id":"(app-pages-browser)/./app/home/<USER>/ProcessFlow.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\home\\components\\ProgressJumbotron.tsx":{"id":"(app-pages-browser)/./app/home/<USER>/ProgressJumbotron.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\home\\components\\Reviews.tsx":{"id":"(app-pages-browser)/./app/home/<USER>/Reviews.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\components\\is-user-login.tsx":{"id":"(app-pages-browser)/./components/is-user-login.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/loading","static/chunks/app/loading.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/loading","static/chunks/app/loading.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\page":["static/css/app/page.css"],"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\loading":[],"C:\\Users\\<USER>\\Desktop\\work\\BoostingCity\\client\\app\\not-found":[]}}