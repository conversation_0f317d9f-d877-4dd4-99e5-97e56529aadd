"use client"
import CustomDropDown from "@/components/custom-dropdown";
import CustomImage from "@/components/custom-image";
import DiscountCodeComponent from "@/components/discountCode";
import DivisionGrid from "@/components/division-grid";
import RanksGrid from "@/components/ranks-grid";
import {
  CurrentLPAction,
  DropDownType,
  GameNames,
  ModifDivision,
  ModifRank,
  RanksType,
} from "@/constants/common";
import {
  LolAccType,
  LolCurrentLPType,
  LolLPDropDown,
  LolLPGainDropDown,
  LolLPGainType,
  LolQueueTypeDropDown,
  LolRankPricing,
  LolRanks,
  LolRnakBoostingdDropDwon,
  LolRoleDropDown,
  LolSeverDropDown,
  Loldivisions,
  PlatformOsType,
  lolRankNames,
  lolRoleType,
  lolplatformOsDropDwon,
} from "@/constants/lol-constants";
import { LolServiceTypes } from "@/constants/service-types";
import { createOrder } from "@/database/functions.database";
import { hoursToDaysHours } from "@/utils/date-time-utils";
import { calcTotalPricePrecentage } from "@/utils/now-payments";
import { errorToast } from "@/utils/toast-utils";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { FunctionComponent, SetStateAction, useEffect, useState } from "react";
import { Bars } from "react-loader-spinner";


interface RankBoostingComponentProps {
  priceControl: any
}

const RankBoostingComponent: FunctionComponent<
  RankBoostingComponentProps
> = ({ priceControl }) => {
  const router = useRouter();
  const [discount, setDiscount] = useState(1);
  const [loading, setLoading] = useState(false);
  const [currentRank, setCurrentRank] = useState<RanksType>(LolRanks[0]);
  const [acceptableRanks, setAcceptableRanks] = useState<ModifRank[]>([]);
  const [accCurrentDivision, setAccCurrentDivision] = useState<ModifDivision[]>(
    []
  );
  const [currentDivision, setCurrentDivision] = useState<ModifDivision>();
  const [desiredRank, setDesiredRank] = useState<RanksType>(LolRanks[0]);
  const [desiredDivision, setDesiredDivision] = useState<ModifDivision>();
  const [accDesiredDivision, setAccDesiredDivision] = useState<ModifDivision[]>(
    []
  );
  //prices
  const [initialPrice, setInitialPrice] = useState<number>(0);
  const [price, setPrice] = useState<number>(0);
  const [lPPrice, setLPPrice] = useState<number>(0);
  const [lPGainPrice, setLPGainPrice] = useState<number>(0);
  const [pwBoosterPrice, setpwBoosterPrice] = useState<number>(0);
  const [priorityOrderPrice, setPriorityOrderPrice] = useState<number>(0);
  const [streamGamesPrice, setStreamGamesPrice] = useState<number>(0);
  const [soloOnlyPrice, setSoloOnlyPrice] = useState<number>(0);

  const [soloOnly, setSoloOnly] = useState(false);
  const [offline, setOffline] = useState(false);
  const [roles, setRoles] = useState(false);
  const [priority, setPriority] = useState(false);
  const [stream, setStream] = useState(false);
  const [boosters, setBoosters] = useState(false);
  const [role, setRole] = useState<lolRoleType>();
  const [queueType, setQueueType] = useState<DropDownType>();

  const [duration, setDuration] = useState<string>("0 hours");
  const [hours, setHours] = useState<number>(0);
  const [AccShared, setAccShared] = useState<LolAccType>();
  const [platformOs, setPlatformOs] = useState<PlatformOsType>();
  const [total, setTotal] = useState<any>({});
  const [priceCt, setPriceCt] = useState<any>({});


  const divisios = Object.keys(Loldivisions).reverse();
  const handleCurrentRankChange = (rank: RanksType) => {
    setDesiredDivision(undefined)
    setCurrentRank(rank);
    const rankNames = Object.keys(lolRankNames);
    const tempRanks = rankNames.slice(rankNames.indexOf(rank.rank!));
    const accRanks = LolRanks.map((lrank) => ({
      ...lrank,
      accept: currentRank && tempRanks.includes(lrank.rank!),
    }));

    accRanks.push({
      id: "rank9",
      title: "Master",
      image: "/images/games/lol/Master.png",
      rank: lolRankNames.Master,
      accept: true,
    });

    setAcceptableRanks(accRanks);
  };

  const handleCurrenDivisionChange = (division: ModifDivision) => {
    setCurrentDivision(division);
    setDesiredDivision(undefined)
  };
  const handleDesiredRankChange = (rank: RanksType) => {
    setDesiredRank(rank);
    setPlatformOs(undefined);
    setAccShared(undefined);
    if (desiredRank.rank === lolRankNames.Master) {
      setDesiredDivision({ title: Loldivisions.IV, accept: false });
    }
    setDesiredDivision(undefined);
  };

  const handleDesiredDivisionChange = (division: ModifDivision) => {
    setDesiredDivision(() => division);
    setAccShared(undefined);
    setPlatformOs(undefined);
  };
  useEffect(() => {
    if (!currentDivision) {
      setInitialPrice(0);
      setPrice(0);
    }
  });

  const updateCurrentLP = (cLP: LolCurrentLPType) => {
    if (currentDivision && desiredDivision) {
      setLPPrice(calculateLP(cLP, initialPrice));
    }
    if (desiredRank.rank === lolRankNames.Master) {
      setLPPrice(calculateLP(cLP, initialPrice));
    }
  };
  const calculateLP = (cLP: LolCurrentLPType, initial: number) => {
    const percentValue = (cLP.value / 100) * initial;
    if (cLP?.action === CurrentLPAction.Decrease) {
      if (cLP.value > 0) {
        return Number(percentValue.toFixed(2));
      } else {
        return 0;
      }
    } else if (cLP?.action === CurrentLPAction.Increase) {
      return Number(percentValue.toFixed(2));
    } else {
      return 0;
    }
  };

  const updateLPGain = (llpGain: LolLPGainType) => {
    if (currentDivision && desiredDivision) {
      setLPGainPrice(calculateLpGain(llpGain, initialPrice));
    }
    if (desiredRank.rank === lolRankNames.Master) {
      setLPGainPrice(calculateLpGain(llpGain, initialPrice));
    }
  };

  const calculateLpGain = (llpGain: LolLPGainType, initial: number) => {
    const percentValue = (llpGain.value / 100) * initial;
    let finalPrice = 0;
    if (llpGain && llpGain?.action === CurrentLPAction.Decrease) {
      if (llpGain?.value > 0) {
        finalPrice = Number(percentValue.toFixed(2));
      } else {
        finalPrice = 0;
      }
    } else if (llpGain?.action === CurrentLPAction.Increase) {
      finalPrice = Number(percentValue.toFixed(2));
    }
    return finalPrice ?? 0;
  };

  useEffect(() => {
    const idel = LolRankPricing.find(
      (pricing) =>
        pricing.currentRank === currentRank.rank &&
        pricing.from === currentDivision?.title &&
        pricing.desiredRank === desiredRank.rank &&
        pricing.to === desiredDivision?.title
    );

    if (idel) {
      setPrice(idel.price);
      setInitialPrice(idel.price);
      setDuration(`${0} days, ${idel.hours} hours`);
    } else {
      if (currentRank && currentDivision) {
        setPriceAndDuration(
          currentRank,
          desiredRank,
          currentDivision,
          desiredDivision
        );
      }
    }
    updateTotalPrice()
  }, [desiredDivision]);

  useEffect(() => {
    if (desiredRank.rank === lolRankNames.Master) {
      // in case of Master Rank there is no divisions
      setAccDesiredDivision(
        divisios.map((divn) => ({
          title: divn as Loldivisions,
          accept: false,
        }))
      );
      setPriceAndDuration(currentRank, desiredRank, currentDivision, {
        title: Loldivisions.IV,
        accept: false,
      });
    } else {
      const tempDivns = divisios.slice(
        divisios.findIndex((divn) => divn === currentDivision?.title) + 1
      );
      setAccDesiredDivision(
        divisios.map((divn) => ({
          title: divn as Loldivisions,
          accept:
            desiredRank.rank === currentRank.rank
              ? tempDivns.includes(divn)
              : true,
        }))
      );
    }

    setAccShared(undefined);
    setPlatformOs(undefined);
    updateTotalPrice()
  }, [currentDivision, currentRank, desiredRank]);

  useEffect(() => {
    const divns = divisios.map((divn) => ({
      title: divn as Loldivisions,
      accept: true,
    }));
    const accRanks = LolRanks.map((lrank) => ({
      ...lrank,
      accept: true,
    }));
    accRanks.push({
      id: "rank9",
      title: "Master",
      image: "/images/games/lol/Master.png",
      rank: lolRankNames.Master,
      accept: true,
    });
    setAcceptableRanks(accRanks);
    setAccDesiredDivision(divns);
    setAccCurrentDivision(divns);
    setDesiredDivision(divns[1]);
    setCurrentDivision(divns[0]);
    getPriceControl()

  }, []);

  // Set Price and Duration based on props
  function setPriceAndDuration(
    cR: RanksType,
    dR: RanksType,
    cD?: ModifDivision,
    dD?: ModifDivision
  ) {
    const startIndex = LolRankPricing.findIndex(
      (pr) => pr.currentRank === cR.rank && pr.from === cD?.title
    );
    const endIndex = LolRankPricing.findIndex(
      (pr) => pr.desiredRank === dR.rank && pr.to === dD?.title
    );

    const sliced = LolRankPricing.slice(startIndex, endIndex + 1);
    let price = 0;
    let hours = 0;
    sliced.forEach((pr) => {
      price += pr.price;
      hours += pr.hours ?? 0;
    });
    setPrice(price);
    setHours(hours);
    setInitialPrice(price);

    //calculate duration
    const { Days, Hours } = hoursToDaysHours(hours);

    setDuration(`${Days} days, ${Hours} hours`);
  }

  const handleSubmit = async () => {
    if (!currentRank) return errorToast("Please select your current Rank!");

    if (!desiredRank) return errorToast("Please select your desired Rank!");

    if (!currentDivision)
      return errorToast("Please select your current Division!");

    if (!desiredDivision)
      return errorToast("Please select your desired Division!");

    if (!role) return errorToast("Please select your role!");

    if (!queueType) return errorToast("Please select your Queue Type!");
    setLoading(true);
    const orderId = await createOrder({
      game_name: GameNames.LeagueOfLegends,
      service_type: LolServiceTypes.RankBoosting,
      order_details: JSON.stringify({
        currentRank,
        currentDivision,
        desiredRank,
        desiredDivision,
        AccShared,
        platformOs,
        role,
        queueType,
      }),
      play_with_boosters: boosters,
      appear_offline: offline,
      champions_roles: roles,
      priority_order: priority,
      stream_game: stream,
      completion_time_hours: hours,
      price: total?.price
    });

    if (orderId) router.push(`/checkout?order_id=${orderId}`);
  };
  const handleAddonesChange = (
    bool: boolean,
    percent: number,
    setChange: (value: SetStateAction<number>) => void
  ) => {
    if (bool) setChange(percent * initialPrice);
    else setChange(0);
  };

  function updateTotalPrice() {
    const fixedPrice = Number(
      (
        (price +
          lPPrice +
          lPGainPrice +
          pwBoosterPrice +
          priorityOrderPrice +
          streamGamesPrice +
          soloOnlyPrice) *
        discount
      ).toFixed(2))
    const result = calcTotalPricePrecentage(priceCt, fixedPrice)
    setTotal(result)

  }
  async function getPriceControl() {
    const fixedPrice = Number(
      (
        (price +
          lPPrice +
          lPGainPrice +
          pwBoosterPrice +
          priorityOrderPrice +
          streamGamesPrice +
          soloOnlyPrice) *
        discount
      ).toFixed(2))

    setPriceCt(priceControl)
    if (priceControl) {
      const result = calcTotalPricePrecentage(priceControl, fixedPrice)
      setTotal(result)
    }
  }


  useEffect(() => {
    handleAddonesChange(boosters, 0.4, setpwBoosterPrice);
    handleAddonesChange(priority, 0.2, setPriorityOrderPrice);
    handleAddonesChange(stream, 0.1, setStreamGamesPrice);
    handleAddonesChange(soloOnly, 0.3, setSoloOnlyPrice);
    updateTotalPrice()
  }, [price, initialPrice, boosters, stream, priority, soloOnly, discount]);

  return (
    <div className="col-span-12">
      <div className="grid grid-cols-12 gap-3 p-3">
        <div className="col-span-12 md:col-span-9 bg-primary-dark rounded-md px-6 py-8">
          <div className="grid grid-cols-12">
            <div className="col-span-12 md:col-span-6">
              <div className="flex gap-2 justify-start items-center h-[100px]">
                <CustomImage
                  src={currentRank.image}
                  alt=""
                  height={105}
                  width={105}
                />
                <div>
                  <h2 className="font-montserrat text-lg font-semibold">
                    Current Rank
                  </h2>
                  <p className="font-montserrat text-primary-grey-text text-sm">
                    {currentRank.title}
                  </p>
                </div>
              </div>
              <RanksGrid
                onSelect={handleCurrentRankChange}
                ranks={LolRanks}
                type="current"
                defaultSelected={currentRank}
              />
              <div className="mt-8">
                <DivisionGrid
                  divisions={accCurrentDivision}
                  onSelect={handleCurrenDivisionChange}
                />
              </div>
                  <div className="w-ful mt-6">

                  <CustomDropDown
                    title={"ACC SHARED OR PLAY W BOOSTERS"}
                    options={LolRnakBoostingdDropDwon}
                    placeholder="ACC SHARED"
                    selectedValue={AccShared}
                    onSelect={(optn) => {
                      updateCurrentLP(optn);
                      setAccShared(optn);
                    }}
                  />
                  </div>
              <div className="grid grid-cols-12 mt-6">
                <div className="flex justify-start gap-2 col-span-10">
                  <CustomDropDown
                    title={"Select Platform"}
                    options={lolplatformOsDropDwon}
                    selectedValue={platformOs}
                    placeholder="Platform"
                    onSelect={(optn: LolLPGainType) => {
                      updateLPGain(optn);
                      setPlatformOs(optn);
                    }}
                  />
                  <CustomDropDown
                    title={"Select Role"}
                    options={LolRoleDropDown}
                    placeholder="Role"
                    selectedValue={role}
                    onSelect={setRole}
                  />
                </div>
              </div>
            </div>
            <div className="col-span-12 md:col-span-6 mt-8 md:mt-0">
              <div className="flex gap-2 justify-start items-center h-[100px]">
                <CustomImage
                  src={desiredRank.image}
                  alt=""
                  height={105}
                  width={105}
                />
                <div>
                  <h2 className="font-montserrat text-lg font-semibold">
                    Desired Rank
                  </h2>
                  <p className="font-montserrat text-primary-grey-text text-sm">
                    {desiredRank.title}
                  </p>
                </div>
              </div>
              <RanksGrid
                onSelect={handleDesiredRankChange}
                ranks={acceptableRanks}
                type="desired"
                defaultSelected={desiredRank}
              />
              <div className="mt-8">
                <DivisionGrid
                  divisions={accDesiredDivision}
                  onSelect={handleDesiredDivisionChange}
                />
              </div>
              {/* <div className="grid grid-cols-12 mt-6">
                <div className="flex justify-start gap-2 col-span-12">
                  <CustomDropDown
                    title={"Queue Type"}
                    options={LolRoleDropDown}
                    placeholder="ROL"
                    selectedValue={queueType}
                    onSelect={setQueueType}
                  />
                </div>
              </div> */}
            </div>
          </div>
        </div>
        <div className="col-span-12 md:col-span-3 bg-primary-dark rounded-md p-2">
          <div className="bg-[#1C1E2B] flex justify-between items-center p-3">
            <div>
              <div className="flex  justify-start items-center h-[80px]">
                <CustomImage
                  src={currentRank.image}
                  height={85}
                  width={85}
                  alt={"rank-boost"}
                />
              </div>
              <p className="text-center">{currentRank.title}</p>
            </div>
            <div>
              <div className="w-fit bg-primary-purple flex justify-center items-center p-2 rounded-3xl">
                <Image
                  className="image"
                  src="/images/icons/arrow-right-short.svg"
                  alt="hero-image"
                  height={22}
                  width={22}
                />
              </div>
            </div>
            <div>
              <div className="flex  justify-start items-center h-[80px]">
                <CustomImage
                  src={desiredRank.image}
                  height={85}
                  width={85}
                  alt={"rank-boost"}
                />
              </div>
              <p className="text-center">{desiredRank.title}</p>
            </div>
          </div>

          <div className="flex justify-between items-center my-4">
            <span className="ms-3 text-sm font-montserrat text-primary-grey-text">
              Play with Boosters (+40%)
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                value="40"
                className="sr-only peer"
                onChange={(e) => {
                  const percentValue = 0.4 * initialPrice;
                  if (e.target.checked) {
                    setpwBoosterPrice(Number(percentValue.toFixed(2)));
                  } else {
                    setpwBoosterPrice(0);
                  }
                  setBoosters((prev) => !prev);
                }}
              />
              <div className="w-11 h-6 bg-primary-dark-grey peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-[#fff] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-purple"></div>
            </label>
          </div>
          <div className="flex justify-between items-center my-4">
            <span className="ms-3 text-sm font-montserrat text-primary-grey-text">
              Appear Offline (Free)
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                value=""
                className="sr-only peer"
                onChange={() => setOffline((prev) => !prev)}
              />
              <div className="w-11 h-6 bg-primary-dark-grey peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-[#fff] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-purple"></div>
            </label>
          </div>
          <div className="flex justify-between items-center my-4">
            <span className="ms-3 text-sm font-montserrat text-primary-grey-text">
              Champions/Roles (Free)
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                value=""
                className="sr-only peer"
                onChange={() => setRoles((prev) => !prev)}
              />
              <div className="w-11 h-6 bg-primary-dark-grey peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-[#fff] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-purple"></div>
            </label>
          </div>
          <div className="flex justify-between items-center my-4">
            <span className="ms-3 text-sm font-montserrat text-primary-grey-text">
              Priority Order (+20%)
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                value=""
                className="sr-only peer"
                onChange={(e) => {
                  const percentValue = (20 / 100) * initialPrice;
                  if (e.target.checked) {
                    setPriorityOrderPrice(Number(percentValue.toFixed(2)));
                  } else {
                    setPriorityOrderPrice(0);
                  }
                  setPriority((prev) => !prev);
                }}
              />
              <div className="w-11 h-6 bg-primary-dark-grey peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-[#fff] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-purple"></div>
            </label>
          </div>
          <div className="flex justify-between items-center my-4">
            <span className="ms-3 text-sm font-montserrat text-primary-grey-text">
              Stream Games (+10%)
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                value=""
                className="sr-only peer"
                onChange={(e) => {
                  const percentValue = (10 / 100) * initialPrice;
                  if (e.target.checked) {
                    setStreamGamesPrice(Number(percentValue.toFixed(2)));
                  } else {
                    setStreamGamesPrice(0);
                  }

                  setStream((prev) => !prev);
                }}
              />
              <div className="w-11 h-6 bg-primary-dark-grey peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-[#fff] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-purple"></div>
            </label>
          </div>
          <div className="flex justify-between items-center my-4">
            <span className="ms-3 text-sm font-montserrat text-primary-grey-text">
              Solo Only (+30%)
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                value={`${soloOnly}`}
                onChange={(e) => {
                  const percentValue = 0.3 * initialPrice;
                  if (e.target.checked) {
                    setSoloOnlyPrice(Number(percentValue.toFixed(2)));
                  } else {
                    setSoloOnlyPrice(0);
                  }
                  setSoloOnly((prev) => !prev);
                }}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-primary-dark-grey peer-focus:outline-none  rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-[#fff] after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-purple"></div>
            </label>
          </div>
          <DiscountCodeComponent setDiscount={setDiscount} />
          <div className="rounded-sm border border-secondary-dark p-3 flex justify-between items-center bg-[#1C1E2B]">
            {/* {total?.percentage !== '' ? (
            <span className="flex justify-between items-center gap-2">
            <span> Main Price :
            <del className={`mx-2 ${ total?.percentage?.includes('-') ? 'text-red-text' : 'text-primary-purple' }`} >
                 {total?.original}
             </del>
            </span>
             <span className={`font-montserrat font-semibold p-2 rounded ms-2 ${total?.percentage?.includes('-') ? 'bg-red-text' : 'bg-primary-purple'}`}>
                {total?.percentage}
             </span>
            
              </span>
                    ) :  '' } */}
            <span className="font-montserrat font-semibold">Total Amount</span>
            <span className="font-montserrat font-semibold">
              $
              {total?.price}
            </span>
          </div>
          {loading ? (
            <button
              disabled
              type="button"
              onClick={handleSubmit}
              className="flex justify-center items-center text-white bg-secondary-600 font-medium w-full  rounded-lg text-sm me-2 mb-2 p-0"
            >
              <Bars
                height="40"
                color="white"
                ariaLabel="bars-loading"
                wrapperStyle={{}}
                wrapperClass=""
                visible={true}
              />
            </button>
          ) : (
            <button
              type="button"
              onClick={handleSubmit}
              className="text-white bg-primary-purple hover:bg-primary-shaded-purple focus:ring-4 w-full focus:ring-blue-300 font-medium rounded-lg text-sm px-7 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
            >
              Rank Up Now
            </button>
          )}

          <div className="ms-3 text-xs font-montserrat text-primary-grey-text text-center">
            Completion Time: {duration}
          </div>
        </div>
      </div>

    </div>
  );
};

export default RankBoostingComponent;
