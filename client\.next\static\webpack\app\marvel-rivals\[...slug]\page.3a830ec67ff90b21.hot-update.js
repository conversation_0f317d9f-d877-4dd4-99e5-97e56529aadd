"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/marvel-rivals/[...slug]/page",{

/***/ "(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx":
/*!*******************************************************************!*\
  !*** ./app/marvel-rivals/[...slug]/components/level-boosting.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n// Custom Dropdown Component\nconst CustomDropDown = (param)=>{\n    let { options, placeholder, selectedValue, onSelect, className = \"\" } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-purple text-white px-6 py-3 rounded-lg cursor-pointer flex items-center justify-center min-w-[200px] hover:bg-primary-purple/80 transition-colors border border-secondary-dark\",\n                onClick: ()=>setIsOpen(!isOpen),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium font-montserrat\",\n                        children: (selectedValue === null || selectedValue === void 0 ? void 0 : selectedValue.label) || placeholder\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"ml-2 w-4 h-4\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-full left-0 right-0 bg-primary-dark-grey border border-secondary-dark rounded-lg mt-1 z-50 shadow-lg\",\n                children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 hover:bg-secondary-dark cursor-pointer text-white transition-colors font-montserrat\",\n                        onClick: ()=>{\n                            onSelect(option);\n                            setIsOpen(false);\n                        },\n                        children: option.label\n                    }, option.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomDropDown, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c = CustomDropDown;\n// Discount Code Component\nconst DiscountCodeComponent = (param)=>{\n    let { setDiscount, discount } = param;\n    _s1();\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [applied, setApplied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const applyDiscount = ()=>{\n        if (code.toLowerCase() === \"discount10\") {\n            setDiscount(0.9);\n            setApplied(true);\n        } else if (code.toLowerCase() === \"save25\") {\n            setDiscount(0.75);\n            setApplied(true);\n        } else {\n            alert('Invalid discount code. Try \"discount10\" or \"save25\"');\n        }\n    };\n    const removeDiscount = ()=>{\n        setDiscount(1);\n        setApplied(false);\n        setCode(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-4 p-3 bg-primary-dark-grey rounded-lg border border-secondary-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: code,\n                        onChange: (e)=>setCode(e.target.value),\n                        placeholder: \"Try: discount10 or save25\",\n                        className: \"flex-1 px-3 py-2 bg-secondary-dark border border-secondary-dark rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple font-montserrat\",\n                        disabled: applied\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: applied ? removeDiscount : applyDiscount,\n                        className: \"px-4 py-2 rounded font-medium transition-colors font-montserrat \".concat(applied ? \"bg-red-600 hover:bg-red-700 text-white\" : \"bg-primary-purple hover:bg-primary-purple/80 text-white\"),\n                        children: applied ? \"Remove\" : \"Apply\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            applied && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-green-400 text-sm mt-1 font-montserrat\",\n                children: [\n                    \"Discount applied successfully! \",\n                    Math.round((1 - discount) * 100),\n                    \"% off\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DiscountCodeComponent, \"a7bggQr9ThxF6zYom1UyTK7Cruc=\");\n_c1 = DiscountCodeComponent;\n// Multi-Range Slider Component\nconst MultiRangeSlider = (param)=>{\n    let { min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange } = param;\n    _s2();\n    const sliderRef = useRef(null);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getPercent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((value)=>Math.round((value - min) / (max - min) * 100), [\n        min,\n        max\n    ]);\n    const handleMouseDown = (type)=>(event)=>{\n            setIsDragging(type);\n            event.preventDefault();\n        };\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        if (!isDragging || !sliderRef.current) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        const percent = Math.min(100, Math.max(0, (event.clientX - rect.left) / rect.width * 100));\n        const value = Math.round(min + percent / 100 * (max - min));\n        if (isDragging === \"current\") {\n            const newCurrent = Math.min(value, desiredLevel - 1);\n            onCurrentChange(Math.max(min, newCurrent));\n        } else if (isDragging === \"desired\") {\n            const newDesired = Math.max(value, currentLevel + 1);\n            onDesiredChange(Math.min(max, newDesired));\n        }\n    }, [\n        isDragging,\n        min,\n        max,\n        currentLevel,\n        desiredLevel,\n        onCurrentChange,\n        onDesiredChange\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setIsDragging(null);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"mouseup\", handleMouseUp);\n            return ()=>{\n                document.removeEventListener(\"mousemove\", handleMouseMove);\n                document.removeEventListener(\"mouseup\", handleMouseUp);\n            };\n        }\n    }, [\n        isDragging,\n        handleMouseMove,\n        handleMouseUp\n    ]);\n    const currentPercent = getPercent(currentLevel);\n    const desiredPercent = getPercent(desiredLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-8 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-4\",\n                ref: sliderRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-6 bg-secondary-dark rounded-lg border border-secondary-dark\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute h-6 bg-gradient-to-r from-primary-yellow to-primary-orange rounded-lg transition-all duration-300\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                width: \"\".concat(desiredPercent - currentPercent, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-6 h-6 bg-primary-yellow rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-primary-yellow/80 z-20\",\n                            style: {\n                                left: \"\".concat(currentPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"current\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-primary-yellow text-black px-2 py-1 rounded text-xs font-bold whitespace-nowrap font-montserrat\",\n                                children: [\n                                    \"Current: \",\n                                    currentLevel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute w-6 h-6 bg-primary-orange rounded-full border-2 border-white shadow-lg transition-all duration-300 cursor-pointer hover:bg-primary-orange/80 z-20\",\n                            style: {\n                                left: \"\".concat(desiredPercent, \"%\"),\n                                transform: \"translateX(-50%)\"\n                            },\n                            onMouseDown: handleMouseDown(\"desired\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-primary-orange text-white px-2 py-1 rounded text-xs font-bold whitespace-nowrap font-montserrat\",\n                                children: [\n                                    \"Target: \",\n                                    desiredLevel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between text-sm text-gray-400 mb-4 font-montserrat\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"START\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"25\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"LOW\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"MID\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"75\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"HIGH\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-bold\",\n                                children: \"100\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: \"MAX\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-primary-dark-grey rounded-lg border border-secondary-dark\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-yellow font-bold text-lg font-montserrat\",\n                                        children: currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"CURRENT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-purple font-bold text-lg font-montserrat\",\n                                        children: desiredLevel - currentLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"LEVELS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-orange font-bold text-lg font-montserrat\",\n                                        children: desiredLevel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 font-montserrat\",\n                                        children: \"TARGET\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-400 font-bold font-montserrat\",\n                            children: [\n                                currentLevel,\n                                \" → \",\n                                desiredLevel,\n                                \" (\",\n                                desiredLevel - currentLevel,\n                                \" levels)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MultiRangeSlider, \"IBff4LxYZyFuRUNRi8ivWheMclo=\");\n_c2 = MultiRangeSlider;\nconst LevelBoostingComponent = ()=>{\n    _s3();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [discount, setDiscount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Level states\n    const [currentLevel, setCurrentLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(21);\n    const [desiredLevel, setDesiredLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(64);\n    // Mock data\n    const LolLevelBoostingDropDown = [\n        {\n            id: \"acc1\",\n            label: \"ACC SHARED\",\n            title: \"ACC SHARED\",\n            value: 0,\n            action: \"None\"\n        },\n        {\n            id: \"acc2\",\n            label: \"PLAY W BOOSTERS (100%)\",\n            title: \"PLAY W BOOSTERS (100%)\",\n            value: 100,\n            action: \"Increase\"\n        },\n        {\n            id: \"acc3\",\n            label: \"PLAY W BOOSTERS (200%)\",\n            title: \"PLAY W BOOSTERS (200%)\",\n            value: 200,\n            action: \"Increase\"\n        }\n    ];\n    const platformOptions = [\n        {\n            id: \"pc\",\n            label: \"PC\",\n            title: \"PC\"\n        },\n        {\n            id: \"console\",\n            label: \"CONSOLE\",\n            title: \"CONSOLE\"\n        }\n    ];\n    const roleOptions = [\n        {\n            id: \"any\",\n            label: \"ANY ROLE\",\n            title: \"ANY ROLE\"\n        },\n        {\n            id: \"tank\",\n            label: \"TANK\",\n            title: \"TANK\"\n        },\n        {\n            id: \"damage\",\n            label: \"DAMAGE\",\n            title: \"DAMAGE\"\n        },\n        {\n            id: \"support\",\n            label: \"SUPPORT\",\n            title: \"SUPPORT\"\n        }\n    ];\n    // Selection states\n    const [accShared, setAccShared] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(LolLevelBoostingDropDown[0]);\n    const [platform, setPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(platformOptions[0]);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(roleOptions[0]);\n    // Price states\n    const [totalPrice, setTotalPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [completionTime, setCompletionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        days: 0,\n        hours: 0\n    });\n    // Add-on states\n    const [boosters, setBoosters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [offline, setOffline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [priority, setPriority] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Level adjustment functions\n    const adjustCurrentLevel = (increment)=>{\n        setCurrentLevel((prev)=>{\n            const newLevel = Math.max(1, Math.min(99, prev + increment));\n            if (newLevel >= desiredLevel) {\n                setDesiredLevel(Math.min(100, newLevel + 1));\n            }\n            return newLevel;\n        });\n    };\n    const adjustDesiredLevel = (increment)=>{\n        setDesiredLevel((prev)=>{\n            const newLevel = Math.max(currentLevel + 1, Math.min(100, prev + increment));\n            return newLevel;\n        });\n    };\n    // Calculate price based on level difference\n    const calculateBasePrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return 5.21;\n        }\n        const maxPrice = 2681.80;\n        const minPrice = 5.21;\n        const priceRange = maxPrice - minPrice;\n        const calculatedPrice = minPrice + (levelDifference - 1) / 98 * priceRange;\n        return Math.max(minPrice, calculatedPrice);\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Calculate completion time\n    const calculateCompletionTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const levelDifference = desiredLevel - currentLevel;\n        if (levelDifference <= 0) {\n            return {\n                days: 0,\n                hours: 1\n            };\n        }\n        const maxDays = 73;\n        const maxHours = 5;\n        const totalMaxHours = maxDays * 24 + maxHours;\n        const totalHours = levelDifference / 99 * totalMaxHours;\n        const days = Math.floor(totalHours / 24);\n        const hours = Math.round(totalHours % 24);\n        return {\n            days,\n            hours\n        };\n    }, [\n        currentLevel,\n        desiredLevel\n    ]);\n    // Calculate total price with add-ons\n    const calculateTotalPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const basePrice = calculateBasePrice();\n        let additionalPrice = 0;\n        if (accShared.label === \"PLAY W BOOSTERS (100%)\") {\n            additionalPrice += basePrice * 1.0;\n        } else if (accShared.label === \"PLAY W BOOSTERS (200%)\") {\n            additionalPrice += basePrice * 2.0;\n        }\n        if (boosters) additionalPrice += basePrice * 0.4;\n        if (priority) additionalPrice += basePrice * 0.2;\n        if (stream) additionalPrice += basePrice * 0.1;\n        const subtotal = basePrice + additionalPrice;\n        const finalPrice = subtotal * discount;\n        return finalPrice;\n    }, [\n        calculateBasePrice,\n        accShared.label,\n        boosters,\n        priority,\n        stream,\n        discount\n    ]);\n    // Handle order creation\n    const handleSubmit = async ()=>{\n        if (desiredLevel <= currentLevel) {\n            alert(\"Desired level must be higher than current level!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            alert(\"Order created successfully!\\nLevel: \".concat(currentLevel, \" → \").concat(desiredLevel, \"\\nTotal: $\").concat(totalPrice.toFixed(2)));\n        } catch (error) {\n            console.error(\"Order creation failed:\", error);\n            alert(\"Failed to create order. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const time = calculateCompletionTime();\n        setCompletionTime(time);\n    }, [\n        calculateCompletionTime\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const price = calculateTotalPrice();\n        setTotalPrice(price);\n    }, [\n        calculateTotalPrice\n    ]);\n    const originalPrice = calculateTotalPrice() / discount;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"col-span-12 bg-primary-dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-12 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-9 bg-primary-dark-grey rounded-md px-6 py-8 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center text-xl font-bold mr-4 text-white\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white font-montserrat\",\n                                                children: \"SELECT YOUR CURRENT AND DESIRED LEVEL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-4xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"CURRENT LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-yellow font-montserrat\",\n                                                                    children: currentLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel <= 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 416,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-yellow text-black px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: currentLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustCurrentLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: currentLevel >= 99,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 426,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold mb-4 text-white font-montserrat\",\n                                                                    children: \"DESIRED LEVEL\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-6xl font-bold mb-4 text-primary-orange font-montserrat\",\n                                                                    children: desiredLevel\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-4 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(-1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel <= currentLevel + 1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 441,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-primary-orange text-white px-4 py-2 rounded-full font-bold text-lg min-w-[60px] font-montserrat\",\n                                                                            children: desiredLevel\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 443,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>adjustDesiredLevel(1),\n                                                                            className: \"w-12 h-12 bg-primary-purple rounded-full flex items-center justify-center hover:bg-primary-purple/80 transition-colors text-white\",\n                                                                            disabled: desiredLevel >= 100,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                size: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                                lineNumber: 451,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MultiRangeSlider, {\n                                                    min: 1,\n                                                    max: 100,\n                                                    currentLevel: currentLevel,\n                                                    desiredLevel: desiredLevel,\n                                                    onCurrentChange: setCurrentLevel,\n                                                    onDesiredChange: setDesiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 grid grid-cols-4 gap-4 max-w-3xl mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary-dark-grey p-4 rounded-lg text-center border border-secondary-dark\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-primary-yellow font-bold text-xl font-montserrat\",\n                                                        children: currentLevel\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1 font-montserrat\",\n                                                        children: \"CURRENT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary-dark-grey p-4 rounded-lg text-center border border-secondary-dark\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-primary-orange font-bold text-xl font-montserrat\",\n                                                        children: desiredLevel\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1 font-montserrat\",\n                                                        children: \"TARGET\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary-dark-grey p-4 rounded-lg text-center border border-secondary-dark\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-400 font-bold text-xl font-montserrat\",\n                                                        children: [\n                                                            \"$\",\n                                                            totalPrice.toFixed(0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1 font-montserrat\",\n                                                        children: \"TOTAL COST\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary-dark-grey p-4 rounded-lg text-center border border-secondary-dark\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-primary-purple font-bold text-xl font-montserrat\",\n                                                        children: completionTime.days > 0 ? \"\".concat(completionTime.days, \"d\") : \"\".concat(completionTime.hours, \"h\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1 font-montserrat\",\n                                                        children: \"EST. TIME\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"ACC SHARED OR PLAY\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white font-montserrat\",\n                                                                children: \"W BOOSTERS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomDropDown, {\n                                                options: LolLevelBoostingDropDown,\n                                                selectedValue: accShared,\n                                                onSelect: setAccShared,\n                                                placeholder: \"ACC SHARED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT ROLE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomDropDown, {\n                                                options: roleOptions,\n                                                selectedValue: role,\n                                                onSelect: setRole,\n                                                placeholder: \"ANY ROLE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-primary-purple rounded-full flex items-center justify-center text-lg font-bold mr-3 text-white\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white font-montserrat\",\n                                                        children: \"SELECT PLATFORM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomDropDown, {\n                                                options: platformOptions,\n                                                selectedValue: platform,\n                                                onSelect: setPlatform,\n                                                placeholder: \"PC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 lg:col-span-3 bg-primary-dark-grey rounded-md p-2 border border-secondary-dark\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-secondary-dark p-4 rounded-lg mb-6 flex items-center justify-center border border-secondary-dark\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400 mb-2 font-montserrat\",\n                                            children: \"BOOST FROM LEVEL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold mb-3 font-montserrat\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-yellow\",\n                                                    children: currentLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 mx-2\",\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-primary-orange\",\n                                                    children: desiredLevel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-primary-yellow to-primary-orange text-white px-4 py-2 rounded-full text-sm font-bold font-montserrat\",\n                                            children: [\n                                                \"+\",\n                                                desiredLevel - currentLevel,\n                                                \" LEVELS\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-secondary-dark p-4 rounded-lg mb-6 border border-secondary-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-white font-montserrat\",\n                                            children: \"APPROXIMATE COMPLETION\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xl font-bold text-center text-white font-montserrat\",\n                                        children: [\n                                            completionTime.days > 0 && \"\".concat(completionTime.days, \" DAYS \"),\n                                            completionTime.hours,\n                                            \" HOURS\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 font-montserrat\",\n                                                children: \"ACTIVE BOOSTERS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-white font-montserrat\",\n                                                        children: \"61\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 pt-3 border-t border-gray-600 text-sm text-gray-400 font-montserrat\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Level Range:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-primary-orange\",\n                                                        children: [\n                                                            currentLevel,\n                                                            \" → \",\n                                                            desiredLevel\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Levels to Boost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-primary-purple\",\n                                                        children: desiredLevel - currentLevel\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Time per Level:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            desiredLevel > currentLevel ? ((completionTime.days * 24 + completionTime.hours) / (desiredLevel - currentLevel)).toFixed(1) : \"0\",\n                                                            \" hrs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 mb-6\",\n                                children: [\n                                    {\n                                        label: \"Play with Boosters (+40%)\",\n                                        state: boosters,\n                                        setter: setBoosters\n                                    },\n                                    {\n                                        label: \"Appear Offline (Free)\",\n                                        state: offline,\n                                        setter: setOffline\n                                    },\n                                    {\n                                        label: \"Champions/Roles (Free)\",\n                                        state: roles,\n                                        setter: setRoles\n                                    },\n                                    {\n                                        label: \"Priority Order (+20%)\",\n                                        state: priority,\n                                        setter: setPriority\n                                    },\n                                    {\n                                        label: \"Stream Games (+10%)\",\n                                        state: stream,\n                                        setter: setStream\n                                    }\n                                ].map((addon, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 font-montserrat\",\n                                                children: addon.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: addon.state,\n                                                        onChange: ()=>addon.setter(!addon.state),\n                                                        className: \"sr-only peer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-purple rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DiscountCodeComponent, {\n                                setDiscount: setDiscount,\n                                discount: discount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-700 p-4 rounded-lg mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center text-xl font-bold mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Total Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    discount < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400 line-through mb-1\",\n                                                        children: [\n                                                            \"Original: $\",\n                                                            originalPrice.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl text-green-400\",\n                                                        children: [\n                                                            \"$\",\n                                                            totalPrice.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    discount < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-green-400\",\n                                                        children: [\n                                                            \"You save: $\",\n                                                            (originalPrice - totalPrice).toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-4 border-t border-gray-600 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Base Price (\",\n                                                            currentLevel,\n                                                            \"→\",\n                                                            desiredLevel,\n                                                            \"):\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateBasePrice().toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            discount < 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-red-400 mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Discount (\",\n                                                                    Math.round((1 - discount) * 100),\n                                                                    \"%):\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"-$\",\n                                                                    (originalPrice - totalPrice).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between font-bold text-green-400 text-base mt-2 pt-2 border-t border-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Final Price:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    totalPrice.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    desiredLevel === 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-lg border border-orange-500/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-orange-400 font-bold text-sm\",\n                                                    children: \"MAX LEVEL BOOST\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white text-xs mt-1\",\n                                                    children: [\n                                                        \"Complete \",\n                                                        currentLevel,\n                                                        \"→100 journey in \",\n                                                        completionTime.days,\n                                                        \" days \",\n                                                        completionTime.hours,\n                                                        \" hours!\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                disabled: true,\n                                className: \"w-full bg-gray-600 text-white py-3 rounded-lg flex justify-center items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSubmit,\n                                className: \"w-full bg-violet-600 hover:bg-violet-700 text-white py-3 rounded-lg font-bold text-lg transition-colors\",\n                                children: \"Level Up Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-xs text-gray-400 mt-4\",\n                                children: [\n                                    \"Completion Time: \",\n                                    completionTime.days,\n                                    \" Days \",\n                                    completionTime.hours,\n                                    \" Hours\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n                lineNumber: 390,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n            lineNumber: 389,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\work\\\\BoostingCity\\\\client\\\\app\\\\marvel-rivals\\\\[...slug]\\\\components\\\\level-boosting.tsx\",\n        lineNumber: 388,\n        columnNumber: 5\n    }, undefined);\n};\n_s3(LevelBoostingComponent, \"I4xZ2l8//fbKv/5I/JTXl5HJEuo=\");\n_c3 = LevelBoostingComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LevelBoostingComponent);\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"CustomDropDown\");\n$RefreshReg$(_c1, \"DiscountCodeComponent\");\n$RefreshReg$(_c2, \"MultiRangeSlider\");\n$RefreshReg$(_c3, \"LevelBoostingComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/marvel-rivals/[...slug]/components/level-boosting.tsx\n"));

/***/ })

});