import { LolLPGainType } from './lol-constants';
import { AccountCardProps } from '@/components/account-card'
import { uid } from '@/utils/string-utils'
import { CurrentAccAction, CurrentLPAction, RankPricingType, RanksType } from './common'

export enum lolRankNames {
  Iron = 'Iron',
  Bronze = 'Bronze',
  Silver = 'Silver',
  Gold = 'Gold',
  Platinum = 'Platinum',
  Emerlad = 'Emerlad',
  Diamond = 'Diamond',
  Master = 'Master',
  GrandMaster = 'GrandMaster',
  Unnranked = 'Unranked',
}

export enum Loldivisions {
  I = 'I',
  II = 'II',
  III = 'III',
  IV = 'IV',
}

export const LolRankPricing: RankPricingType[] = [
  {
    currentRank: lolRankNames.Iron,
    desiredRank: lolRankNames.Iron,
    from: Loldivisions.IV,
    to: Loldivisions.III,
    price: 6,
    perWinPrice: 2.8,
    placementPrice: 2.8,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Iron,
    desiredRank: lolRankNames.Iron,
    from: Loldivisions.III,
    to: Loldivisions.II,
    price: 6,
    perWinPrice: 2.8,
    placementPrice: 2.8,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Iron,
    desiredRank: lolRankNames.Iron,
    from: Loldivisions.II,
    to: Loldivisions.I,
    price: 6,
    perWinPrice: 2.8,
    placementPrice: 2.8,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Iron,
    desiredRank: lolRankNames.Bronze,
    from: Loldivisions.I,
    to: Loldivisions.IV,
    price: 6,
    perWinPrice: 2.8,
    placementPrice: 2.8,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Bronze,
    desiredRank: lolRankNames.Bronze,
    from: Loldivisions.IV,
    to: Loldivisions.III,
    price: 6.5,
    perWinPrice: 2.8,
    placementPrice: 2.8,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Bronze,
    desiredRank: lolRankNames.Bronze,
    from: Loldivisions.III,
    to: Loldivisions.II,
    price: 6.5,
    perWinPrice: 2.8,
    placementPrice: 2.8,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Bronze,
    desiredRank: lolRankNames.Bronze,
    from: Loldivisions.II,
    to: Loldivisions.I,
    price: 6.5,
    perWinPrice: 2.8,
    placementPrice: 2.8,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Bronze,
    desiredRank: lolRankNames.Silver,
    from: Loldivisions.I,
    to: Loldivisions.IV,
    price: 7.5,
    perWinPrice: 2.8,
    placementPrice: 2.8,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Silver,
    desiredRank: lolRankNames.Silver,
    from: Loldivisions.IV,
    to: Loldivisions.III,
    price: 7.5,
    perWinPrice: 4,
    placementPrice: 3,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Silver,
    desiredRank: lolRankNames.Silver,
    from: Loldivisions.III,
    to: Loldivisions.II,
    price: 7.5,
    perWinPrice: 4,
    placementPrice: 3,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Silver,
    desiredRank: lolRankNames.Silver,
    from: Loldivisions.II,
    to: Loldivisions.I,
    price: 7.5,
    perWinPrice: 4,
    placementPrice: 3,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Silver,
    desiredRank: lolRankNames.Gold,
    from: Loldivisions.I,
    to: Loldivisions.IV,
    price: 12.5,
    perWinPrice: 4,
    placementPrice: 3,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Gold,
    desiredRank: lolRankNames.Gold,
    from: Loldivisions.IV,
    to: Loldivisions.III,
    price: 13,
    perWinPrice: 5.5,
    placementPrice: 4,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Gold,
    desiredRank: lolRankNames.Gold,
    from: Loldivisions.III,
    to: Loldivisions.II,
    price: 14,
    perWinPrice: 5.5,
    placementPrice: 4,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Gold,
    desiredRank: lolRankNames.Gold,
    from: Loldivisions.II,
    to: Loldivisions.I,
    price: 17,
    perWinPrice: 5.5,
    placementPrice: 4,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Gold,
    desiredRank: lolRankNames.Platinum,
    from: Loldivisions.I,
    to: Loldivisions.IV,
    price: 19,
    perWinPrice: 5.5,
    placementPrice: 4,
    hours: 5,
  },
  {
    currentRank: lolRankNames.Platinum,
    desiredRank: lolRankNames.Platinum,
    from: Loldivisions.IV,
    to: Loldivisions.III,
    price: 21,
    perWinPrice: 8.5,
    placementPrice: 5,
    hours: 6,
  },
  {
    currentRank: lolRankNames.Platinum,
    desiredRank: lolRankNames.Platinum,
    from: Loldivisions.III,
    to: Loldivisions.II,
    price: 23,
    perWinPrice: 8.5,
    placementPrice: 5,
    hours: 6,
  },
  {
    currentRank: lolRankNames.Platinum,
    desiredRank: lolRankNames.Platinum,
    from: Loldivisions.II,
    to: Loldivisions.I,
    price: 26,
    perWinPrice: 8.5,
    placementPrice: 5,
    hours: 6,
  },
  {
    currentRank: lolRankNames.Platinum,
    desiredRank: lolRankNames.Emerlad,
    from: Loldivisions.I,
    to: Loldivisions.IV,
    price: 32,
    perWinPrice: 8.5,
    placementPrice: 5,
    hours: 6,
  },
  {
    currentRank: lolRankNames.Emerlad,
    desiredRank: lolRankNames.Emerlad,
    from: Loldivisions.IV,
    to: Loldivisions.III,
    price: 37,
    perWinPrice: 12.5,
    placementPrice: 6,
    hours: 6,
  },
  {
    currentRank: lolRankNames.Emerlad,
    desiredRank: lolRankNames.Emerlad,
    from: Loldivisions.III,
    to: Loldivisions.II,
    price: 38,
    perWinPrice: 12.5,
    placementPrice: 6,
    hours: 6,
  },
  {
    currentRank: lolRankNames.Emerlad,
    desiredRank: lolRankNames.Emerlad,
    from: Loldivisions.II,
    to: Loldivisions.I,
    price: 41,
    perWinPrice: 12.5,
    placementPrice: 6,
    hours: 6,
  },
  {
    currentRank: lolRankNames.Emerlad,
    desiredRank: lolRankNames.Diamond,
    from: Loldivisions.I,
    to: Loldivisions.IV,
    price: 43,
    perWinPrice: 12.5,
    placementPrice: 6,
    hours: 6,
  },
  {
    currentRank: lolRankNames.Diamond,
    desiredRank: lolRankNames.Diamond,
    from: Loldivisions.IV,
    to: Loldivisions.III,
    price: 62,
    perWinPrice: 16,
    placementPrice: 8,
    hours: 24,
  },
  {
    currentRank: lolRankNames.Diamond,
    desiredRank: lolRankNames.Diamond,
    from: Loldivisions.III,
    to: Loldivisions.II,
    price: 76,
    perWinPrice: 18,
    placementPrice: 8,
    hours: 24,
  },
  {
    currentRank: lolRankNames.Diamond,
    desiredRank: lolRankNames.Diamond,
    from: Loldivisions.II,
    to: Loldivisions.I,
    price: 105,
    perWinPrice: 20,
    placementPrice: 8,
    hours: 24,
  },
  {
    currentRank: lolRankNames.Diamond,
    desiredRank: lolRankNames.Master,
    from: Loldivisions.I,
    to: Loldivisions.IV,
    price: 125,
    perWinPrice: 22,
    placementPrice: 8,
    hours: 24,
  },
  {
    currentRank: lolRankNames.Master,
    desiredRank: lolRankNames.Master,
    from: Loldivisions.IV,
    to: Loldivisions.III,
    price: 0,
    perWinPrice: 24,
    placementPrice: 10,
    hours: 0,
  },
  {
    currentRank: lolRankNames.Master,
    desiredRank: lolRankNames.Master,
    from: Loldivisions.III,
    to: Loldivisions.II,
    price: 0,
    perWinPrice: 26,
    placementPrice: 10,
    hours: 0,
  },
  {
    currentRank: lolRankNames.Master,
    desiredRank: lolRankNames.Master,
    from: Loldivisions.II,
    to: Loldivisions.I,
    price: 0,
    perWinPrice: 28,
    placementPrice: 10,
    hours: 0,
  },
  {
    currentRank: lolRankNames.Master,
    desiredRank: lolRankNames.GrandMaster,
    from: Loldivisions.I,
    to: Loldivisions.IV,
    price: 0,
    perWinPrice: 30,
    placementPrice: 10,
    hours: 0,
  },
  {
    currentRank: lolRankNames.GrandMaster,
    desiredRank: lolRankNames.GrandMaster,
    from: Loldivisions.IV,
    to: Loldivisions.I,
    price: 0,
    perWinPrice: 32,
    placementPrice: 13,
    hours: 0,
  },
]
export const LolRanks: RanksType[] = [
  {
    id: 'rank1',
    title: 'Iron',
    image: '/images/games/lol/Iron.png',
    rank: lolRankNames.Iron,
  },
  {
    id: 'rank2',
    title: 'Bronze',
    image: '/images/games/lol/Bronze.png',
    rank: lolRankNames.Bronze,
  },
  {
    id: 'rank3',
    title: 'Silver',
    image: '/images/games/lol/Silver.png',
    rank: lolRankNames.Silver,
  },
  {
    id: 'rank4',
    title: 'Gold',
    image: '/images/games/lol/Gold.png',
    rank: lolRankNames.Gold,
  },
  {
    id: 'rank6',
    title: 'Platinum',
    image: '/images/games/lol/Platinum.png',
    rank: lolRankNames.Platinum,
  },
  {
    id: 'rank7',
    title: 'Emerlad',
    image: '/images/games/lol/Diamond.png',
    rank: lolRankNames.Emerlad,
  },
  {
    id: 'rank8',
    title: 'Diamond',
    image: '/images/games/lol/Emerlad.png',
    rank: lolRankNames.Diamond,
  },
]
export interface LolCurrentLPType {
  id: string
  label: string
  value: number
  action: CurrentLPAction
}
// If current LP is 0-20 = Price is normal
// If current Lp is 21-40 = Decrease price by 15%
// If current LP is 41-60 = Decrease price by 30%
// If current LP is 61-80 = Decrease price by 45%
// If current LP is 81-80 = Decrease price by 60%

export const LolLPDropDown: LolCurrentLPType[] = [
  {
    id: uid(),
    label: '0-20',
    value: 0,
    action: CurrentLPAction.Increase,
  },
  {
    id: uid(),
    label: '21-40',
    value: 15,
    action: CurrentLPAction.Decrease,
  },
  {
    id: uid(),
    label: '41-60',
    value: 30,
    action: CurrentLPAction.Decrease,
  },
  {
    id: uid(),
    label: '61-80',
    value: 45,
    action: CurrentLPAction.Decrease,
  },
  {
    id: uid(),
    label: '81-100',
    value: 60,
    action: CurrentLPAction.Decrease,
  },
]

export interface LolLPGainType {
  id: string
  label: string
  value: number
  action: CurrentLPAction
}
export interface LolAccType {
  id: string
  label: string
  value: number
  action: CurrentAccAction
}
//  IF LP gain is 23+ = Price is normal
//  If LP gain is 18-22 = Increase price by 20%
//  IF lp gain is 8-17 = Increase price by 50%
export const LolLPGainDropDown: LolLPGainType[] = [
  {
    id: uid(),
    label: 'Very low 8-17',
    value: 50,
    action: CurrentLPAction.Increase,
  },
  {
    id: uid(),
    label: 'Low 18-22',
    value: 20,
    action: CurrentLPAction.Increase,
  },
  {
    id: uid(),
    label: 'Normal +23',
    value: 0,
    action: CurrentLPAction.Increase,
  },
]


//  IF ACC PLAY W1 BOOSTERS (100%) PRICE INCREASE 5$
//  IF ACC PLAY W1 BOOSTERS (200%) PRICE INCREASE 10$

export const LolRnakBoostingdDropDwon :LolAccType[]=[
  {
    id: uid(),
    label: 'AAC SHARED',
    value: 0,
    action: CurrentAccAction.Increase,
  },
  {
    id: uid(),
    label: 'PLAY W1 BOOSTERS (100%)',
    value: 5,
    action: CurrentAccAction.Increase,
  },
  {
    id: uid(),
    label: 'PLAY W1 BOOSTERS (200%)',
    value: 10,
    action:CurrentAccAction.Increase,
  },
]

export interface LolAccountLevelingType {
  from: number
  to: number
  price: number
}
// From level 1 to 10 4$ per one level
// From level 10 to 20 5$ per one level
// From elvel 20 to 30 12$ per one level
// From level 30 to 40 14$ per level
// From level 40 to 50 18$ per level
export const LolAccountLeveling: LolAccountLevelingType[] = [
  {
    from: 1,
    to: 10,
    price: 4,
  },
  {
    from: 10,
    to: 20,
    price: 5,
  },
  {
    from: 20,
    to: 30,
    price: 12,
  },
  {
    from: 30,
    to: 40,
    price: 14,
  },
  {
    from: 40,
    to: 50,
    price: 18,
  },
]
export const LolSeverDropDown: { id: string; label: string; value: string }[] = [
  {
    id: uid(),
    label: 'North America',
    value: 'North America',
  },
  {
    id: uid(),
    label: 'EU-West',
    value: 'EU-West',
  },
  {
    id: uid(),
    label: 'Oceania',
    value: 'Oceania',
  },
  {
    id: uid(),
    label: 'Turkey',
    value: 'Turkey',
  },
  {
    id: uid(),
    label: 'Russia',
    value: 'Russia',
  },
]

export const LolQueueTypeDropDown: {
  id: string
  label: string
  value: string
}[] = [
  {
    id: uid(),
    label: 'Solo',
    value: 'Solo',
  },
  {
    id: uid(),
    label: 'Duo',
    value: 'Duo',
  },
  {
    id: uid(),
    label: 'Flex',
    value: 'Flex',
  },
]

export enum LolMasteryLevels {
  M0 = 'm0',
  M1 = 'm1',
  M2 = 'm2',
  M3 = 'm3',
  M4 = 'm4',
  M5 = 'm5',
  M6 = 'm6',
  M50 = 'm50',
  M51 = 'm51',
}

export interface LolChampionMasteryPricingType {
  from: LolMasteryLevels
  to: LolMasteryLevels
  price: number
  hours?: number
}

export const LolMasteryPricing: LolChampionMasteryPricingType[] = [
  {
    from: LolMasteryLevels.M0,
    to: LolMasteryLevels.M1,
    price: 3,
    hours: 2,
  },
  {
    from: LolMasteryLevels.M1,
    to: LolMasteryLevels.M2,
    price: 5,
    hours: 3,
  },
  {
    from: LolMasteryLevels.M2,
    to: LolMasteryLevels.M3,
    price: 9,
    hours: 5,
  },
]

export const LolCurrentMastery: {
  id: string
  label: string
  value: LolMasteryLevels
  image: string
}[] = [
  {
    id: uid(),
    label: 'Mastery 0',
    value: LolMasteryLevels.M0,
    image: '/images/games/lol/champion-mastery/c-m-0.png',
  },
  {
    id: uid(),
    label: 'Mastery 1',
    value: LolMasteryLevels.M1,
    image: '/images/games/lol/champion-mastery/c-m-1.png',
  },
  {
    id: uid(),
    label: 'Mastery 2',
    value: LolMasteryLevels.M2,
    image: '/images/games/lol/champion-mastery/c-m-2.png',
  },
  {
    id: uid(),
    label: 'Mastery 3',
    value: LolMasteryLevels.M3,
    image: '/images/games/lol/champion-mastery/c-m-3.png',
  },
  {
    id: uid(),
    label: 'Mastery 4',
    value: LolMasteryLevels.M4,
    image: '/images/games/lol/champion-mastery/c-m-3.png',
  },
  {
    id: uid(),
    label: 'Mastery 5',
    value: LolMasteryLevels.M5,
    image: '/images/games/lol/champion-mastery/c-m-3.png',
  },
  {
    id: uid(),
    label: 'Mastery 6',
    value: LolMasteryLevels.M6,
    image: '/images/games/lol/champion-mastery/c-m-3.png',
  },
]
export const LolDesiredMastery: {
  id: string
  label: string
  value: string
}[] = [
  {
    id: uid(),
    label: 'Mastery 0',
    value: LolMasteryLevels.M0,
  },
  {
    id: uid(),
    label: 'Mastery 1',
    value: LolMasteryLevels.M1,
  },
  {
    id: uid(),
    label: 'Mastery 2',
    value: LolMasteryLevels.M2,
  },
  {
    id: uid(),
    label: 'Mastery 3',
    value: LolMasteryLevels.M3,
  },
  {
    id: uid(),
    label: 'Mastery 4',
    value: LolMasteryLevels.M4,
  },
  {
    id: uid(),
    label: 'Mastery 5 0 Tokens',
    value: LolMasteryLevels.M50,
  },
  {
    id: uid(),
    label: 'Mastery 5 1 Tokens',
    value: LolMasteryLevels.M51,
  },
]

export const LolChampionMasteryPricing = []

export const LolBoostingFAQs = [
  {
    head: 'How does League of Legends Boosting work?',
    body: 'Upon acquiring our boosting service, a seasoned player will log into your account and play on your behalf, working towards achieving specific objectives such as reaching a higher rank.',
  },
  {
    head: 'Is League of Legends Boosting secure?',
    body: "Absolutely. Your account's security is our utmost priority. Our boosters employ secure methods and take precautions to uphold the confidentiality and integrity of your account.",
  },
  {
    head: 'How can I monitor the progress of my League of Legends Boost?',
    body: 'Utilize our user-friendly dashboard to access real-time updates on the progress of your boost. Additionally, our customer support team is available 24/7 to address any inquiries you may have.',
  },
  {
    head: 'Can I use my account during the League of Legends Boost?',
    body: "To ensure security and efficiency, it's recommended to refrain from playing on your account during the boost. However, we can explore accommodating specific requests in certain cases.",
  },
]
export const LolCoachingFAQs = [
  {
    head: '1. What is League of Legends coaching?',
    body: 'League of Legends coaching is a personalized service where experienced players, often high-ranking or professional, provide guidance and insights to help you improve your gameplay, weapon selection, decision-making, and overall performance in League of Legends.',
  },
  {
    head: '2. How does the coaching process work?',
    body: 'After selecting a coach, you`ll schedule sessions at your convenience. During sessions, the coach will review your gameplay, identify areas for improvement, provide tips, and answer your questions. Some coaches also offer ongoing support between sessions.',
  },
  {
    head: '3. How can I choose the right League of Legends coach for me?',
    body: 'BoostingCity offers a diverse selection of League of Legends coaches. Consider factors such as the coach`s rank, coaching style, and expertise with specific weapons or roles. You can also read reviews from other clients to help make an informed decision.',
  },
  {
    head: '4. Can I request coaching for a specific aspect of my gameplay?',
    body: 'Absolutely! Coaches can tailor sessions to focus on specific aspects, such as weapon accuracy, map awareness, teamwork, or decision-making. Communicate your goals with the coach, and they`ll customize the sessions accordingly.',
  },
  {
    head: '5. Is coaching suitable for players of all skill levels?',
    body: 'Yes, coaching is beneficial for players of all skill levels, from beginners to experienced players. Coaches adapt their approach to match your current skill level and help you progress based on your unique needs.',
  },
  {
    head: '6. How long is each coaching session?',
    body: 'Coaching sessions typically last between 1 to 2 hours, but the duration can be adjusted based on your preferences and the coach`s availability.',
  },
  {
    head: '7. Can I schedule multiple coaching sessions?',
    body: 'Certainly! Many clients opt for a series of coaching sessions to ensure continuous improvement. You can schedule sessions at regular intervals to monitor your progress and receive ongoing guidance.',
  },
  {
    head: '8. What happens if I need to reschedule a coaching session?',
    body: 'Life happens, and we understand that. If you need to reschedule a session, contact your coach as early as possible to arrange an alternative time that suits both parties.',
  },
  {
    head: '9. Is coaching only for ranked players?',
    body: 'No, coaching is beneficial for players in all game modes, including normal and ranked. Coaches can help you refine your skills, optimize your strategy, and enhance your overall understanding of the game, regardless of the mode you play.',
  },
  {
    head: '10. What if I`m not satisfied with the coaching service?',
    body: 'Your satisfaction is our priority. If you`re not pleased with a coaching session, please reach out to our customer support team, and we`ll work with you to address any concerns or provide an alternative solution.',
  },
]
export const LolAccountsFAQs = [
  {
    head: '1. Why should I choose BoostingCity for my League of Legends account?',
    body: 'BoostingCity is a trusted platform for purchasing secure and high-quality League of Legends accounts, verified for authenticity.',
  },
  {
    head: '2. Can I customize my League of Legends account after purchase?',
    body: 'Absolutely. Upon purchasing an League of Legends account from BoostingCity, you have full ownership and can customize it to fit your preferences.',
  },
  {
    head: '3. quickly will I receive my League of Legends account after purchase?',
    body: 'Swift delivery is a priority. Once your purchase is confirmed, expect to receive your League of Legends account details promptly.',
  },
  {
    head: '4. Can I contact BoostingCity for assistance or inquiries?',
    body: 'Yes, our customer support is available 24/7 for any questions or assistance you may need before or after your purchase.',
  },
  {
    head: '5. What guarantees do you offer for League of Legends account security?',
    body: 'BoostingCity guarantees the security and authenticity of the League of Legends accounts listed on our platform.',
  },
  {
    head: '6. What if I encounter issues with my League of Legends account post-purchase?',
    body: 'Contact our customer support immediately if you face any issues post-purchase, and we`ll work towards a resolution.',
  },
  {
    head: '7. What types of League of Legends accounts does BoostingCity offer?',
    body: 'BoostingCity provides various League of Legends accounts with different legends, skins, and other in-game features.',
  },
]
export const LolAccountProps: AccountCardProps[] = [
  {
    title: 'Master',
    subtitle: 'NA',
    image: '/images/games/lol/account-master.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Emerald III',
    subtitle: 'NA',
    image: '/images/games/lol/account-emerald.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Gold IV',
    subtitle: 'NA',
    image: '/images/games/lol/account-gold.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Diamond II',
    subtitle: 'NA',
    image: '/images/games/lol/account-diamond.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Master',
    subtitle: 'NA',
    image: '/images/games/lol/account-master.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Emerald III',
    subtitle: 'NA',
    image: '/images/games/lol/account-emerald.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Gold IV',
    subtitle: 'NA',
    image: '/images/games/lol/account-gold.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Diamond II',
    subtitle: 'NA',
    image: '/images/games/lol/account-diamond.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Master',
    subtitle: 'NA',
    image: '/images/games/lol/account-master.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Emerald III',
    subtitle: 'NA',
    image: '/images/games/lol/account-emerald.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Gold IV',
    subtitle: 'NA',
    image: '/images/games/lol/account-gold.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Diamond II',
    subtitle: 'NA',
    image: '/images/games/lol/account-diamond.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Master',
    subtitle: 'NA',
    image: '/images/games/lol/account-master.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Emerald III',
    subtitle: 'NA',
    image: '/images/games/lol/account-emerald.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Gold IV',
    subtitle: 'NA',
    image: '/images/games/lol/account-gold.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Diamond II',
    subtitle: 'NA',
    image: '/images/games/lol/account-diamond.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Master',
    subtitle: 'NA',
    image: '/images/games/lol/account-master.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Emerald III',
    subtitle: 'NA',
    image: '/images/games/lol/account-emerald.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Gold IV',
    subtitle: 'NA',
    image: '/images/games/lol/account-gold.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
  {
    title: 'Diamond II',
    subtitle: 'NA',
    image: '/images/games/lol/account-diamond.png',
    props: [
      { title: 'Champions', value: '28' },
      { title: 'Skins', value: '0' },
      { title: 'Blue Essence', value: '11775' },
    ],
    price: 329.99,
  },
]

// Approx completion time from Iron to Platinum=  5 hours per rank
// Approx completion time from Platinum to diamond IV= 6 hours per rank
// Approx completion time from Diamond IV to Master= 1 day per rank
export interface LolCompletionTimeType {
  from: lolRankNames
  to: lolRankNames
  hours: number
}
export const lolCompletionTime: LolCompletionTimeType[] = [
  { from: lolRankNames.Iron, to: lolRankNames.Platinum, hours: 5 },
  { from: lolRankNames.Platinum, to: lolRankNames.Diamond, hours: 5 },
  { from: lolRankNames.Iron, to: lolRankNames.Platinum, hours: 24 },
]

export interface PlatformOsType{
  id:string
  label:string
  value:string | number
}

export const lolplatformOsDropDwon:PlatformOsType[]=[
  {
  id:uid(),
  label:"PC",
  value:"PC",
},
 {
  id:uid(),
  label:"XBOX",
  value:"Xbox",
},
 {
  id:uid(),
  label:"PS5",
  value:"PS5",
},
]

export interface lolRoleType {
  id:string,
  label:string,
  value:string,
  avg:string
}

export const LolRoleDropDown:lolRoleType[]=[
  {
    id:uid(),
    label:"ANY ROLE",
    value:"any role",
    avg:'0'
  },{
      id:uid(),
      label:"DUELIST",
      value:"duelist",  
          avg:'0'

  },
  {
      id:uid(),
      label:"VANGUARD",
      value:"vanguard",
          avg:'0'

  },{
      id:uid(),
      label:"STRATEGIST(+10%)",
      value:"STRATEGIST",
          avg:'10'

  }

]