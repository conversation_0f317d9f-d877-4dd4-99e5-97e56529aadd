import { Minus, Plus } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';

// Custom Dropdown Component
interface DropdownOption {
  id: string;
  label: string;
  title: string;
  value?: number;
  action?: string;
}

interface CustomDropDownProps {
  options: DropdownOption[];
  placeholder: string;
  selectedValue: DropdownOption | null;
  onSelect: (option: DropdownOption) => void;
  className?: string;
}

const CustomDropDown: React.FC<CustomDropDownProps> = ({ options, placeholder, selectedValue, onSelect, className = "" }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={`relative ${className}`}>
      <div
        className="bg-primary-purple text-white px-6 py-3 rounded-lg cursor-pointer flex items-center justify-center min-w-[200px] hover:bg-primary-purple/80 transition-colors border border-secondary-dark"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="font-medium font-montserrat">
          {selectedValue?.label || placeholder}
        </span>
        <svg className="ml-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
        </svg>
      </div>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 bg-primary-dark-grey border border-secondary-dark rounded-lg mt-1 z-50 shadow-lg">
          {options.map((option) => (
            <div
              key={option.id}
              className="px-4 py-2 hover:bg-secondary-dark cursor-pointer text-white transition-colors font-montserrat"
              onClick={() => {
                onSelect(option);
                setIsOpen(false);
              }}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Discount Code Component
interface DiscountCodeComponentProps {
  setDiscount: (discount: number) => void;
  discount: number;
}

const DiscountCodeComponent: React.FC<DiscountCodeComponentProps> = ({ setDiscount, discount }) => {
  const [code, setCode] = useState('');
  const [applied, setApplied] = useState(false);

  const applyDiscount = () => {
    if (code.toLowerCase() === 'discount10') {
      setDiscount(0.9);
      setApplied(true);
    } else if (code.toLowerCase() === 'save25') {
      setDiscount(0.75);
      setApplied(true);
    } else {
      alert('Invalid discount code. Try "discount10" or "save25"');
    }
  };

  const removeDiscount = () => {
    setDiscount(1);
    setApplied(false);
    setCode('');
  };

  return (
    <div className="my-4 p-3 bg-primary-dark-grey rounded-lg border border-secondary-dark">
      <div className="flex gap-2">
        <input
          type="text"
          value={code}
          onChange={(e) => setCode(e.target.value)}
          placeholder="Try: discount10 or save25"
          className="flex-1 px-3 py-2 bg-secondary-dark border border-secondary-dark rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple font-montserrat"
          disabled={applied}
        />
        <button
          onClick={applied ? removeDiscount : applyDiscount}
          className={`px-4 py-2 rounded font-medium transition-colors font-montserrat ${
            applied 
              ? 'bg-red-600 hover:bg-red-700 text-white' 
              : 'bg-primary-purple hover:bg-primary-purple/80 text-white'
          }`}
        >
          {applied ? 'Remove' : 'Apply'}
        </button>
      </div>
      {applied && (
        <p className="text-green-400 text-sm mt-1 font-montserrat">
          Discount applied successfully! {Math.round((1 - discount) * 100)}% off
        </p>
      )}
    </div>
  );
};

// Multi-Range Slider Component
interface MultiRangeSliderProps {
  min: number;
  max: number;
  currentLevel: number;
  desiredLevel: number;
  onCurrentChange: (value: number) => void;
  onDesiredChange: (value: number) => void;
}

const MultiRangeSlider: React.FC<MultiRangeSliderProps> = ({ min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange }) => {
  const sliderRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<'current' | 'desired' | null>(null);
  const [dragOffset, setDragOffset] = useState(0);

  // Level markers for the slider
  const levelMarkers = [1, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100];

  const getPercent = useCallback((value: number) => {
    return ((value - min) / (max - min)) * 100;
  }, [min, max]);

  const getValueFromPercent = useCallback((percent: number) => {
    return Math.round(min + (percent / 100) * (max - min));
  }, [min, max]);

  const handleMouseDown = (type: 'current' | 'desired') => (event: React.MouseEvent) => {
    if (!sliderRef.current) return;
    
    setIsDragging(type);
    const rect = sliderRef.current.getBoundingClientRect();
    const handlePosition = type === 'current' ? getPercent(currentLevel) : getPercent(desiredLevel);
    const clickPosition = ((event.clientX - rect.left) / rect.width) * 100;
    setDragOffset(clickPosition - handlePosition);
    event.preventDefault();
  };

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isDragging || !sliderRef.current) return;

    const rect = sliderRef.current.getBoundingClientRect();
    const percent = Math.min(100, Math.max(0, ((event.clientX - rect.left) / rect.width) * 100 - dragOffset));
    const value = getValueFromPercent(percent);

    if (isDragging === 'current') {
      const newCurrent = Math.min(Math.max(min, value), desiredLevel - 1);
      onCurrentChange(newCurrent);
    } else if (isDragging === 'desired') {
      const newDesired = Math.max(Math.min(max, value), currentLevel + 1);
      onDesiredChange(newDesired);
    }
  }, [isDragging, min, max, currentLevel, desiredLevel, onCurrentChange, onDesiredChange, dragOffset, getValueFromPercent]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(null);
    setDragOffset(0);
  }, []);

  const handleTrackClick = useCallback((event: React.MouseEvent) => {
    if (!sliderRef.current || isDragging) return;

    const rect = sliderRef.current.getBoundingClientRect();
    const percent = ((event.clientX - rect.left) / rect.width) * 100;
    const value = getValueFromPercent(percent);

    const currentPercent = getPercent(currentLevel);
    const desiredPercent = getPercent(desiredLevel);
    const clickPercent = percent;

    // Determine which handle is closer to the click
    const distanceToCurrent = Math.abs(clickPercent - currentPercent);
    const distanceToDesired = Math.abs(clickPercent - desiredPercent);

    if (distanceToCurrent < distanceToDesired) {
      // Move current level handle
      const newCurrent = Math.min(Math.max(min, value), desiredLevel - 1);
      onCurrentChange(newCurrent);
    } else {
      // Move desired level handle
      const newDesired = Math.max(Math.min(max, value), currentLevel + 1);
      onDesiredChange(newDesired);
    }
  }, [currentLevel, desiredLevel, min, max, onCurrentChange, onDesiredChange, getPercent, getValueFromPercent, isDragging]);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const currentPercent = getPercent(currentLevel);
  const desiredPercent = getPercent(desiredLevel);

  return (
    <div className="px-8 mb-6">
      <div className="relative mb-6" ref={sliderRef}>
        {/* Track container */}
        <div 
          className="relative h-8 bg-secondary-dark rounded-lg border border-secondary-dark cursor-pointer"
          onClick={handleTrackClick}
        >
          {/* Background track */}
          <div className="absolute inset-0 bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg" />
          
          {/* Progress between current and desired */}
          <div 
            className="absolute h-8 bg-gradient-to-r from-primary-yellow to-primary-orange rounded-lg transition-all duration-200 shadow-inner"
            style={{ 
              left: `${currentPercent}%`, 
              width: `${Math.max(0, desiredPercent - currentPercent)}%` 
            }}
          />
          
          {/* Level marker ticks */}
          {levelMarkers.map((level) => {
            const percent = getPercent(level);
            return (
              <div
                key={level}
                className="absolute w-0.5 h-8 bg-gray-500 opacity-50"
                style={{ left: `${percent}%` }}
              />
            );
          })}
          
          {/* Current level handle */}
          <div 
            className={`absolute w-8 h-8 bg-primary-yellow rounded-full border-3 border-white shadow-lg transition-all duration-200 cursor-grab z-30 flex items-center justify-center ${
              isDragging === 'current' ? 'cursor-grabbing scale-110' : 'hover:scale-105'
            }`}
            style={{ 
              left: `${currentPercent}%`, 
              transform: 'translateX(-50%)'
            }}
            onMouseDown={handleMouseDown('current')}
          >
            <div className="w-3 h-3 bg-black rounded-full" />
            <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-primary-yellow text-black px-3 py-1 rounded-lg text-xs font-bold whitespace-nowrap font-montserrat shadow-lg">
              Current: {currentLevel}
            </div>
          </div>
          
          {/* Desired level handle */}
          <div 
            className={`absolute w-8 h-8 bg-primary-orange rounded-full border-3 border-white shadow-lg transition-all duration-200 cursor-grab z-30 flex items-center justify-center ${
              isDragging === 'desired' ? 'cursor-grabbing scale-110' : 'hover:scale-105'
            }`}
            style={{ 
              left: `${desiredPercent}%`, 
              transform: 'translateX(-50%)'
            }}
            onMouseDown={handleMouseDown('desired')}
          >
            <div className="w-3 h-3 bg-white rounded-full" />
            <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-primary-orange text-white px-3 py-1 rounded-lg text-xs font-bold whitespace-nowrap font-montserrat shadow-lg">
              Target: {desiredLevel}
            </div>
          </div>
        </div>
      </div>
      
      {/* Level markers */}
      <div className="relative mb-4">
        <div className="flex justify-between text-sm text-gray-400 font-montserrat">
          {levelMarkers.map((level) => (
            <div key={level} className="text-center flex-1">
              <div className="font-bold text-white">{level}</div>
              {level === 1 && <div className="text-xs">START</div>}
              {level === 100 && <div className="text-xs">MAX</div>}
            </div>
          ))}
        </div>
      </div>
      
      {/* Level Range Info */}
      <div className="p-4 bg-primary-dark-grey rounded-lg border border-secondary-dark">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-primary-yellow font-bold text-lg font-montserrat">{currentLevel}</div>
            <div className="text-xs text-gray-400 font-montserrat">CURRENT</div>
          </div>
          <div>
            <div className="text-primary-purple font-bold text-lg font-montserrat">{desiredLevel - currentLevel}</div>
            <div className="text-xs text-gray-400 font-montserrat">LEVELS</div>
          </div>
          <div>
            <div className="text-primary-orange font-bold text-lg font-montserrat">{desiredLevel}</div>
            <div className="text-xs text-gray-400 font-montserrat">TARGET</div>
          </div>
        </div>
        <div className="mt-3 text-center">
          <div className="text-green-400 font-bold font-montserrat">
            {currentLevel} → {desiredLevel} ({desiredLevel - currentLevel} levels)
          </div>
        </div>
      </div>
    </div>
  );
};

export { CustomDropDown, DiscountCodeComponent, MultiRangeSlider };
